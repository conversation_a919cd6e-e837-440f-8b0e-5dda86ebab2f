/**
 * 流式 API 调用模块
 *
 * 功能：
 * - 调用实际的流式工作流 API
 * - 解析 SSE (Server-Sent Events) 流式响应
 * - 处理流式数据并回调给调用方
 * - 实现完整的工作流调用链：data_report → insight_report → competitor_report → planning_scheme
 * - 提供参数验证和错误处理
 * - 支持 thinking1 数据的聚合和流式传输
 */

import type { WorkflowName } from './workflowStatusManager'
import { fetchEventSourceWithTimeout } from '@/utils/streamTimeout'
import { streamingDataManager } from '../components/StreamingDataManager'
import { workflowStatusManager } from './workflowStatusManager'

/**
 * 解码HTML实体并处理转义字符
 */
function decodeHtmlEntitiesAndEscapes(str: string): string {
  let decoded = str

  console.log('[decodeHtmlEntitiesAndEscapes] 输入:', str)

  /** 处理HTML实体编码 */
  decoded = decoded.replace(/&quot;/g, '"')
  decoded = decoded.replace(/&amp;/g, '&')
  decoded = decoded.replace(/&lt;/g, '<')
  decoded = decoded.replace(/&gt;/g, '>')
  decoded = decoded.replace(/&#x27;/g, '\'')
  decoded = decoded.replace(/&#x2F;/g, '/')

  console.log('[decodeHtmlEntitiesAndEscapes] HTML实体解码后:', decoded)

  /**
   * 处理多重转义字符
   * 处理三重转义 \\\\\" -> \"
   */
  decoded = decoded.replace(/\\\\\\\"/g, '\\"')
  /** 处理双重转义 \\\" -> \" */
  decoded = decoded.replace(/\\\\\"/g, '\\"')
  /** 处理单重转义 \" -> " */
  decoded = decoded.replace(/\\"/g, '"')
  /** 处理其他转义字符 */
  decoded = decoded.replace(/\\'/g, '\'')
  decoded = decoded.replace(/\\\\/g, '\\')

  console.log('[decodeHtmlEntitiesAndEscapes] 最终输出:', decoded)

  return decoded
}

/**
 * 流式接口响应数据结构
 */
export interface StreamResponse {
  event: string
  data: string
  id?: string
}

/**
 * 解析的流式数据结构 - 明确区分不同用途的数据类型
 */
export interface ParsedStreamData {
  type: 'report_display' | 'report_title' | 'thinking_stream' | 'workflow_param' | 'complete' | 'error'
  content: string
  title?: string
  error?: string
  nodeTitle?: string
  nodeIsFinish?: boolean
  executionId?: string
}

/**
 * 工作流请求参数结构
 */
export interface WorkflowRequest {
  taskInstanceId: string
  platform: string
  workflowName: string
  parameters: Record<string, any>
}

/**
 * 收集的数据结构 - 用于工作流间数据传递
 */
export interface CollectedData {
  daren_data?: string
  biji_data?: string
  brand_data?: string
  competitor_data?: string
  product_data?: string
  brand_report?: string
  industry_report?: string
  industry_data?: string
  insight_report?: string
  competitor_report?: string
  /** duibiao_disassembly_text 工作流数据字段 */
  笔记title?: string
  笔记正文?: string
  标签?: string
  title?: string
  content?: string
  tag?: string
  image?: any
  [key: string]: any
}

/**
 * API 配置
 */
const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '',
  STREAM_ENDPOINT: '/app/market/stream/execute-main',
  PLATFORM: 'rednote',
}

/**
 * 内存中的工作流输出数据存储
 * 避免localStorage存储大量数据导致浏览器内存爆满
 */
const workflowOutputCache = new Map<string, CollectedData>()

/**
 * 工作流执行参数存储
 * 用于存储每个报告类型的 executionId 和 taskInstanceId
 */
const workflowExecutionParams = new Map<string, {
  executionId: string
  taskInstanceId: string
  timestamp: number
}>()

/**
 * 清理工作流输出缓存
 */
export function clearWorkflowCache(): void {
  workflowOutputCache.clear()
}

/**
 * 获取工作流输出缓存状态 - 用于调试
 */
export function getWorkflowCacheStatus(): Record<string, string[]> {
  const status: Record<string, string[]> = {}
  workflowOutputCache.forEach((data, workflowName) => {
    status[workflowName] = Object.keys(data)
  })
  return status
}

/**
 * 获取工作流输出数据
 */
function getWorkflowOutput(workflowName: string): CollectedData {
  return workflowOutputCache.get(workflowName) || {}
}

/**
 * 保存工作流输出数据
 */
function setWorkflowOutput(workflowName: string, data: CollectedData): void {
  workflowOutputCache.set(workflowName, data)
}

/**
 * 保存工作流执行参数
 */
export function setWorkflowExecutionParams(reportType: string, executionId: string, taskInstanceId: string): void {
  workflowExecutionParams.set(reportType, {
    executionId,
    taskInstanceId,
    timestamp: Date.now(),
  })
  /** 不在这里打印日志，由调用方打印专门格式的日志 */
}

/**
 * 获取工作流执行参数
 */
export function getWorkflowExecutionParams(reportType: string): { executionId: string, taskInstanceId: string } | null {
  const params = workflowExecutionParams.get(reportType)
  if (params) {
    return { executionId: params.executionId, taskInstanceId: params.taskInstanceId }
  }
  return null
}

/**
 * 根据 executionId 查找报告类型
 */
export function getReportTypeByExecutionId(executionId: string): string | null {
  for (const [reportType, params] of workflowExecutionParams.entries()) {
    if (params.executionId === executionId) {
      return reportType
    }
  }
  return null
}

/**
 * 获取所有工作流执行参数状态 - 用于调试
 */
export function getWorkflowExecutionParamsStatus(): Record<string, { executionId: string, taskInstanceId: string, timestamp: number }> {
  const status: Record<string, { executionId: string, taskInstanceId: string, timestamp: number }> = {}
  workflowExecutionParams.forEach((params, reportType) => {
    status[reportType] = params
  })
  return status
}

/**
 * 获取用户认证token
 */
async function getUserToken(): Promise<string> {
  try {
    const { userStore } = await import('@/store/userStore')
    const token = userStore.token
    if (!token) {
      throw new Error('用户未登录，无法获取认证token')
    }
    return token
  }
  catch {
    throw new Error('获取用户认证信息失败')
  }
}

/**
 * 获取或生成taskInstanceId
 */
function getTaskInstanceId(): string {
  /** 尝试从localStorage获取现有的taskInstanceId */
  const taskInstanceId = localStorage.getItem('confirmed_taskInstanceId') || ''
  return taskInstanceId
}

/**
 * 解析SSE数据行 - 支持新的流式数据格式
 * 数据格式示例：{"content":"红","node_title":"insight_report1","node_seq_id":"2079","node_is_finish":false,"token":null,"ext":null}
 */
function parseSSELine(line: string, workflowName?: string, extractedParams?: { executionId: string | null, taskInstanceId: string | null }): ParsedStreamData | null {
  try {
    /** 跳过非data行 */
    if (!line.startsWith('data:')) {
      return null
    }

    /** 提取data内容 */
    const dataContent = line.substring(5).trim()
    if (!dataContent || dataContent === '[DONE]') {
      return {
        type: 'complete',
        content: '',
      }
    }

    /** 解析JSON数据 */
    const data = JSON.parse(dataContent)

    /** 尝试从每个数据块中提取 executionId 和 taskInstanceId */
    if (extractedParams) {
      /** 检查各种可能的字段名 */
      if ((data.execution_id || data.executionId) && !extractedParams.executionId) {
        extractedParams.executionId = data.execution_id || data.executionId
      }
      if ((data.task_instance_id || data.taskInstanceId) && !extractedParams.taskInstanceId) {
        extractedParams.taskInstanceId = data.task_instance_id || data.taskInstanceId
      }

      /** 检查是否是Done事件，Done事件可能包含executionId */
      if (data.event === 'Done' || data.__internal__event === 'Done') {
        if (data.executionId && !extractedParams.executionId) {
          extractedParams.executionId = data.executionId
        }
        if (data.taskInstanceId && !extractedParams.taskInstanceId) {
          extractedParams.taskInstanceId = data.taskInstanceId
        }
      }
    }

    if (data.content) {
      const nodeTitle = data.node_title || ''
      const nodeIsFinish = data.node_is_finish || false

      /** 1. 报告标题数据：用于显示报告头部的title */
      if (nodeTitle === 'report_name_of_planning'
        || nodeTitle === 'competitor_report_name'
        || nodeTitle === 'insight_report_name') {
        return {
          type: 'report_title',
          content: data.content,
          title: data.content,
          nodeTitle,
        }
      }

      /** 2. ThinkingStream数据：精确匹配thinking，传递给ThinkingStream组件 */
      if (nodeTitle === 'thinking') {
        return {
          type: 'thinking_stream',
          content: data.content,
          nodeTitle,
        }
      }

      /** 3. StreamingThinkingStream 数据：根据工作流类型获取不同的数据源 */
      const isThinkingData = (
        (workflowName === 'data_report' && nodeTitle === 'brand_reasoning_content')
        || (workflowName === 'insight_report' && nodeTitle === 'thinking1')
        || (workflowName === 'competitor_report' && nodeTitle === 'thinking1')
      )

      /** 4. planning_scheme thinking1 数据：推送到专用流 */
      const isPlanningThinking1 = (workflowName === 'planning_scheme' && nodeTitle === 'thinking1')

      if (isThinkingData) {
        /** 推送到聚合流 */
        if (workflowName) {
          streamingDataManager.pushThinking1Data(workflowName, data.content)
        }

        return {
          type: 'report_display',
          content: data.content,
          nodeTitle,
        }
      }

      if (isPlanningThinking1) {
        /** 推送到专用的 planning thinking1 流 */
        streamingDataManager.pushPlanningThinking1Data(data.content)

        return {
          type: 'report_display',
          content: data.content,
          nodeTitle,
        }
      }

      /** 4. ReportPreview显示数据：用于拼接到右侧ReportPreview组件内进行流式显示 */
      if (nodeTitle === 'insight_report1'
        || nodeTitle === 'competitor_report1'
        || nodeTitle === 'planning_report1') {
        return {
          type: 'report_display',
          content: data.content,
          nodeTitle,
        }
      }

      /** 4. 后续工作流参数数据：作为后续工作流的输入参数 */
      if (nodeTitle && data.content) {
        return {
          type: 'workflow_param',
          content: data.content,
          nodeTitle,
          nodeIsFinish,
        }
      }
    }

    return null
  }
  catch {
    return null
  }
}

/**
 * 从localStorage获取data_report参数（参照handleFormSubmit逻辑）
 */
function getDataReportParameters(): Record<string, any> {
  const dataReportParams = JSON.parse(localStorage.getItem('dataReportParams') || '{}')

  return {
    brand_name: dataReportParams.brand_name || '',
    competitor_name: dataReportParams.competitor_name || '',
    industry_id: dataReportParams.industry_id || '83',
    industry_name: dataReportParams.industry_name || '',
    product_name: dataReportParams.product_name || '',
    platform: dataReportParams.platform || API_CONFIG.PLATFORM,
  }
}

/**
 * 构造insight_report参数（参照ChatPage.tsx第891-905行逻辑）
 * 使用data_report的输出数据作为入参
 */
function getInsightReportParameters(dataReportData: CollectedData): Record<string, any> {
  const baseParams = getDataReportParameters()

  return {
    brand_name: baseParams.brand_name,
    industry_name: baseParams.industry_name,
    competitor_name: baseParams.competitor_name,
    product_name: baseParams.product_name,
    industry_data: dataReportData.industry_data || '',
    daren_data: dataReportData.daren_data || '',
    biji_data: dataReportData.biji_data || '',
    brand_data: dataReportData.brand_data || '',
    competitor_data: dataReportData.competitor_data || '',
    product_data: dataReportData.product_data || '',
    brand_report: dataReportData.brand_report || '',
    industry_report: dataReportData.industry_report || '',
    platform: API_CONFIG.PLATFORM,
  }
}

/**
 * 构造competitor_report参数（参照ChatPage.tsx第1024-1040行逻辑）
 * 使用data_report和insight_report的输出数据作为入参
 */
function getCompetitorReportParameters(
  dataReportData: CollectedData,
  insightData: CollectedData,
): Record<string, any> {
  const baseParams = getDataReportParameters()

  return {
    brand_name: baseParams.brand_name,
    industry_name: baseParams.industry_name,
    competitor_name: baseParams.competitor_name,
    product_name: baseParams.product_name,
    industry_data: dataReportData.industry_data || '',
    daren_data: dataReportData.daren_data || '',
    biji_data: dataReportData.biji_data || '',
    brand_data: dataReportData.brand_data || '',
    competitor_data: dataReportData.competitor_data || '',
    product_data: dataReportData.product_data || '',
    brand_report: dataReportData.brand_report || '',
    industry_report: dataReportData.industry_report || '',
    insight_report: insightData.insight_report || insightData.insight_report_name || '',
    thinking: insightData.thinking || '',
    platform: API_CONFIG.PLATFORM,
  }
}

/**
 * 构造planning_scheme参数
 * 使用data_report、insight_report和competitor_report的输出数据作为入参
 */
function getPlanningSchemeParameters(
  dataReportData: CollectedData,
  insightData: CollectedData,
  competitorData: CollectedData,
): Record<string, any> {
  const baseParams = getDataReportParameters()

  return {
    brand_report: dataReportData.brand_report || '',
    industry_report: dataReportData.industry_report || '',
    insight_report: insightData.insight_report || '',
    competitor_report: competitorData.competitor_report || '',
    product_name: baseParams.product_name,
    brand_name: baseParams.brand_name,
    platform: API_CONFIG.PLATFORM,
  }
}

/**
 * 构造 duibiao_disassembly_text 参数
 * 从表单数据和 distill_daren_list 工作流获取参数
 * 恢复原本复杂的参数获取逻辑
 */
function getDuibiaoDisassemblyTextParameters(): Record<string, any> {
  const baseParams = getDataReportParameters()
  const dataReportParams = JSON.parse(localStorage.getItem('dataReportParams') || '{}')

  /** 获取基础参数 */
  const productDescription = baseParams.product || dataReportParams.product || ''
  const brandName = baseParams.brand_name || dataReportParams.brand_name || ''
  const productName = baseParams.product_name || dataReportParams.product_name || ''
  const pic = baseParams.pic || dataReportParams.pic || ''

  /** 从 distill_daren_list 工作流获取数据 */
  const distillDarenData = getWorkflowOutput('distill_daren_list')

  /** 复杂的 biji 数据获取逻辑 - 恢复原本的优先级获取 */
  let bijiContent = ''
  let bijiTitle = ''
  let bijiUrl = ''

  if (distillDarenData && distillDarenData.biji_list) {
    let bijiList = distillDarenData.biji_list

    /** 如果是字符串，尝试解析 */
    if (typeof bijiList === 'string') {
      try {
        bijiList = JSON.parse(bijiList)
      }
      catch (e) {
        console.warn('[getDuibiaoDisassemblyTextParameters] 解析 biji_list 失败:', e)
      }
    }

    /** 从 biji_list 中按优先级获取数据：KOL → KOC → suren */
    if (Array.isArray(bijiList) && bijiList.length > 0) {
      const bijiData = bijiList[0]

      /** 优先从 KOL 获取 */
      if (bijiData.kol && bijiData.kol.length > 0) {
        const firstKol = bijiData.kol[0]
        if (firstKol.info) {
          bijiTitle = firstKol.info.title || ''
          bijiContent = firstKol.info.desc || ''
        }
        bijiUrl = firstKol.noteLink || ''
      }

      /** 如果没有 KOL，从 KOC 获取 */
      if (!bijiTitle && bijiData.koc && bijiData.koc.length > 0) {
        const firstKoc = bijiData.koc[0]
        if (firstKoc.info) {
          bijiTitle = firstKoc.info.title || ''
          bijiContent = firstKoc.info.desc || ''
        }
        bijiUrl = firstKoc.noteLink || ''
      }

      /** 如果还没有，从 suren 获取 */
      if (!bijiTitle && bijiData.suren && bijiData.suren.length > 0) {
        const firstSuren = bijiData.suren[0]
        if (firstSuren.info) {
          bijiTitle = firstSuren.info.title || ''
          bijiContent = firstSuren.info.desc || ''
        }
        bijiUrl = firstSuren.noteLink || ''
      }
    }
  }

  /** 如果没有获取到，使用默认值 */
  if (!bijiUrl) {
    bijiUrl = 'https://www.xiaohongshu.com/explore/6891bf9b000000002501e60c'
  }

  /** 复杂的 daren_list 获取逻辑 */
  let darenListStr = ''

  /** 优先从工作流缓存获取 */
  if (distillDarenData && distillDarenData.daren_list) {
    darenListStr = typeof distillDarenData.daren_list === 'string'
      ? distillDarenData.daren_list
      : JSON.stringify(distillDarenData.daren_list)
  }

  /** 如果没有从缓存获取到，尝试从全局store获取（需要通过trendAg获取） */
  if (!darenListStr) {
    try {
      /** 动态导入以避免循环依赖 */
      const { trendAg } = require('./index')
      const stateStore = trendAg.stateStore

      if (stateStore.distillDarenListData) {
        const darenData = {
          KOL: stateStore.distillDarenListData.KOL || [],
          KOC: stateStore.distillDarenListData.KOC || [],
          Regulars: stateStore.distillDarenListData.Regulars || [],
        }
        darenListStr = JSON.stringify(darenData)
      }
    }
    catch (e) {
      console.warn('[getDuibiaoDisassemblyTextParameters] 获取 stateStore 失败:', e)
    }
  }

  /** 获取 brand_report - 从 formData.role 获取，不是从 data_report 工作流 */
  let brandReport = ''
  try {
    const { trendAg } = require('./index')
    const stateStore = trendAg.stateStore
    brandReport = stateStore.formData?.role || ''
  }
  catch (e) {
    console.warn('[getDuibiaoDisassemblyTextParameters] 获取 brand_report 失败:', e)
  }

  return {
    product_description: productDescription,
    biji_content: bijiContent,
    biji_url: bijiUrl,
    biji_title: bijiTitle,
    daren_list: darenListStr,
    brand_name: brandName,
    product_name: productName,
    brand_report: brandReport,
    pic,
    platform: API_CONFIG.PLATFORM,
  }
}

/**
 * 封装data_report流式数据收集逻辑
 * 根据选中代码中的data_report SSE流式数据处理逻辑进行收集
 */
function collectDataReportData(parsedData: ParsedStreamData, collectedData: CollectedData): void {
  if (parsedData.type === 'workflow_param' && parsedData.nodeTitle) {
    const nodeTitle = parsedData.nodeTitle

    /** 收集data_report的关键数据字段 */
    const dataReportFields = [
      'daren_data',
      'biji_data',
      'brand_data',
      'competitor_data',
      'product_data',
      'brand_report',
      'industry_report',
      'industry_data',
      'brand_reasoning_content', // StreamingThinkingStream 需要的数据
    ]

    if (dataReportFields.includes(nodeTitle)) {
      if (!collectedData[nodeTitle]) {
        collectedData[nodeTitle] = ''
      }
      collectedData[nodeTitle] += parsedData.content
    }
  }
}

/**
 * 封装insight_report流式数据收集逻辑
 */
function collectInsightReportData(parsedData: ParsedStreamData, collectedData: CollectedData): void {
  if (parsedData.type === 'workflow_param' && parsedData.nodeTitle) {
    const nodeTitle = parsedData.nodeTitle

    /** 收集insight_report的关键数据字段 */
    const insightReportFields = ['insight_report', 'thinking', 'insight_report_name']

    if (insightReportFields.includes(nodeTitle)) {
      if (!collectedData[nodeTitle]) {
        collectedData[nodeTitle] = ''
      }
      collectedData[nodeTitle] += parsedData.content
    }
  }
}

/**
 * 封装competitor_report流式数据收集逻辑
 */
function collectCompetitorReportData(parsedData: ParsedStreamData, collectedData: CollectedData): void {
  if (parsedData.type === 'workflow_param' && parsedData.nodeTitle) {
    const nodeTitle = parsedData.nodeTitle

    /** 收集competitor_report的关键数据字段 */
    const competitorReportFields = ['competitor_report', 'competitor_report_name']

    if (competitorReportFields.includes(nodeTitle)) {
      if (!collectedData[nodeTitle]) {
        collectedData[nodeTitle] = ''
      }
      collectedData[nodeTitle] += parsedData.content
    }
  }
}

/**
 * 封装 duibiao_disassembly_text 流式数据收集逻辑
 */
function collectDuibiaoDisassemblyTextData(parsedData: ParsedStreamData, collectedData: CollectedData): void {
  if (parsedData.type === 'workflow_param' && parsedData.nodeTitle) {
    const nodeTitle = parsedData.nodeTitle

    /** 收集 duibiao_disassembly_text 的关键数据字段 */
    const duibiaoFields = ['title', 'content', 'tag', 'image']

    if (duibiaoFields.includes(nodeTitle)) {
      /** 特殊处理 content 字段的分片拼接 */
      if (nodeTitle === 'content') {
        /** content 字段需要按序拼接 */
        if (!collectedData['笔记正文']) {
          collectedData['笔记正文'] = ''
          collectedData.content = ''
        }
        collectedData['笔记正文'] += parsedData.content
        collectedData.content += parsedData.content
      }
      /** 其他字段直接映射 */
      else if (nodeTitle === 'title') {
        collectedData['笔记title'] = parsedData.content
        collectedData.title = parsedData.content
      }
      else if (nodeTitle === 'tag') {
        collectedData['标签'] = parsedData.content
        collectedData.tag = parsedData.content
      }
      else if (nodeTitle === 'image') {
        /** 处理image的JSON解析 */
        try {
          let processedImages = parsedData.content
          if (typeof processedImages === 'string' && processedImages.startsWith('[')) {
            const firstParse = JSON.parse(processedImages)
            if (Array.isArray(firstParse) && firstParse.length > 0 && typeof firstParse[0] === 'string') {
              try {
                const secondParse = JSON.parse(firstParse[0])
                if (Array.isArray(secondParse)) {
                  processedImages = secondParse
                }
              }
              catch {
                processedImages = firstParse
              }
            }
          }
          collectedData[nodeTitle] = processedImages
        }
        catch {
          collectedData[nodeTitle] = parsedData.content
        }
      }
      else {
        collectedData[nodeTitle] = parsedData.content
      }
    }
  }
}

/**
 * 封装planning_scheme流式数据收集逻辑
 */
function collectPlanningSchemeData(parsedData: ParsedStreamData, collectedData: CollectedData): void {
  if (parsedData.type === 'workflow_param' && parsedData.nodeTitle) {
    const nodeTitle = parsedData.nodeTitle

    /** 收集planning_scheme的关键数据字段 */
    const planningSchemeFields = ['planning_report', 'report_name_of_planning', 'thinking1']

    if (planningSchemeFields.includes(nodeTitle)) {
      if (!collectedData[nodeTitle]) {
        collectedData[nodeTitle] = ''
      }
      collectedData[nodeTitle] += parsedData.content
    }
  }
}

/**
 * 封装modify_report流式数据收集逻辑
 */
function collectModifyReportData(parsedData: ParsedStreamData, collectedData: CollectedData): void {
  if (parsedData.type === 'workflow_param' && parsedData.nodeTitle) {
    const nodeTitle = parsedData.nodeTitle

    /** 收集modify_report的关键数据字段 */
    const modifyReportFields = ['modified_report']

    if (modifyReportFields.includes(nodeTitle)) {
      if (!collectedData[nodeTitle]) {
        collectedData[nodeTitle] = ''
      }
      collectedData[nodeTitle] += parsedData.content
    }
  }
}

/**
 * 执行流式API请求的通用函数
 */
async function executeStreamRequest(
  workflowName: string,
  parameters: Record<string, any>,
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<CollectedData> {
  try {
    /** 通知工作流开始 */
    if (['insight_report', 'competitor_report', 'planning_scheme'].includes(workflowName)) {
      workflowStatusManager.notifyStatus(workflowName as WorkflowName, 'started')
    }

    const token = await getUserToken()
    const taskInstanceId = getTaskInstanceId()

    const requestParams: WorkflowRequest = {
      taskInstanceId,
      platform: API_CONFIG.PLATFORM,
      workflowName,
      parameters,
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.STREAM_ENDPOINT}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestParams),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`)
    }

    if (!response.body) {
      throw new Error('响应体为空')
    }

    /** executionId 和 taskInstanceId 将在流式输出过程中或最后 done 事件中提供 */

    /** 处理SSE流式数据 */
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    const collectedData: CollectedData = {}

    let buffer = ''
    let chunkCount = 0
    /** 用于在流式处理过程中收集参数 */
    const extractedParams = { executionId: null as string | null, taskInstanceId: null as string | null }

    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        /** 在流式输出完成时，尝试从最后的数据中提取 executionId 和 taskInstanceId */
        /** 这些参数通常在工作流完成的 done 事件中提供 */
        if (buffer.trim()) {
          try {
            const finalData = JSON.parse(buffer.trim())
            if ((finalData.execution_id || finalData.executionId) && !extractedParams.executionId) {
              extractedParams.executionId = finalData.execution_id || finalData.executionId
            }
            if ((finalData.task_instance_id || finalData.taskInstanceId) && !extractedParams.taskInstanceId) {
              extractedParams.taskInstanceId = finalData.task_instance_id || finalData.taskInstanceId
            }
          }
          catch {
            // Error handling without console output
          }
        }

        break
      }

      chunkCount++
      const chunk = decoder.decode(value, { stream: true })
      buffer += chunk

      /** 按行分割，但保留最后一个可能不完整的行 */
      const lines = buffer.split('\n')
      buffer = lines.pop() || ''

      for (const line of lines) {
        if (line.trim() === '')
          continue

        try {
          /** 解析SSE数据，同时尝试提取参数 */
          const parsedData = parseSSELine(line, workflowName, extractedParams)
          if (parsedData) {
            onData(parsedData)

            /** 根据工作流类型收集特定的数据字段 */
            if (workflowName === 'data_report') {
              collectDataReportData(parsedData, collectedData)
            }
            else if (workflowName === 'insight_report') {
              collectInsightReportData(parsedData, collectedData)
            }
            else if (workflowName === 'competitor_report') {
              collectCompetitorReportData(parsedData, collectedData)
            }
            else if (workflowName === 'planning_scheme') {
              collectPlanningSchemeData(parsedData, collectedData)
            }
            else if (workflowName === 'modify_report') {
              collectModifyReportData(parsedData, collectedData)
            }
            else if (workflowName === 'duibiao_disassembly_text') {
              collectDuibiaoDisassemblyTextData(parsedData, collectedData)
            }

            /** 兼容旧的数据收集方式 - 用于显示内容的数据类型 */
            if (parsedData.type === 'report_display' || parsedData.type === 'thinking_stream') {
              if (line.includes('node_title')) {
                const match = line.match(/"node_title":"([^"]+)"/)
                if (match) {
                  const nodeTitle = match[1]
                  /** 收集用于显示的特定node_title，包括StreamingThinkingStream需要的数据 */
                  if (nodeTitle === 'insight_report1' || nodeTitle === 'competitor_report1'
                    || nodeTitle === 'planning_report1' || nodeTitle === 'thinking1'
                    || nodeTitle === 'brand_reasoning_content') {
                    if (!collectedData[nodeTitle]) {
                      collectedData[nodeTitle] = ''
                    }
                    collectedData[nodeTitle] += parsedData.content
                  }
                }
              }
            }
          }
        }
        catch {
          // Error handling without console output
        }
      }
    }

    onComplete?.()

    /** 保存工作流输出数据到内存缓存 */
    if (Object.keys(collectedData).length > 0) {
      setWorkflowOutput(workflowName, collectedData)
    }

    /** 保存主要报告工作流的执行参数 */
    if (workflowName === 'insight_report' || workflowName === 'competitor_report' || workflowName === 'planning_scheme') {
      /** 确定报告类型 */
      let reportType = workflowName
      if (workflowName === 'planning_scheme') {
        reportType = 'planning_report'
      }

      /** 改进的参数提取和验证逻辑 */
      let realExecutionId: string
      let realTaskInstanceId: string

      /** 验证提取到的参数 */
      if (extractedParams.executionId && extractedParams.taskInstanceId) {
        /** 理想情况：两个参数都成功提取且不同 */
        if (extractedParams.executionId !== extractedParams.taskInstanceId) {
          realExecutionId = extractedParams.executionId
          realTaskInstanceId = extractedParams.taskInstanceId
        }
        else {
          /** 提取到的两个参数相同，这可能是错误的 */
          realExecutionId = extractedParams.executionId
          realTaskInstanceId = taskInstanceId /** 使用原始的 taskInstanceId 来确保不同 */
        }
      }
      else if (extractedParams.executionId) {
        /** 只提取到 executionId */
        realExecutionId = extractedParams.executionId
        realTaskInstanceId = taskInstanceId
      }
      else if (extractedParams.taskInstanceId) {
        /** 只提取到 taskInstanceId */
        realExecutionId = extractedParams.taskInstanceId
        realTaskInstanceId = taskInstanceId
      }
      else {
        /** 都没有提取到，使用生成的唯一ID */
        realExecutionId = `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
        realTaskInstanceId = taskInstanceId
      }

      /** 最终验证：确保两个ID不同 */
      if (realExecutionId === realTaskInstanceId) {
        realExecutionId = `${realExecutionId}_exec`
      }

      /** 保存执行参数 */
      setWorkflowExecutionParams(reportType, realExecutionId, realTaskInstanceId)
    }

    /** 通知工作流完成 */
    if (['insight_report', 'competitor_report', 'planning_scheme'].includes(workflowName)) {
      workflowStatusManager.notifyStatus(workflowName as WorkflowName, 'completed', collectedData)
    }

    onComplete?.()
    return collectedData
  }
  catch (error) {
    /** 通知工作流错误 */
    if (['insight_report', 'competitor_report', 'planning_scheme'].includes(workflowName)) {
      workflowStatusManager.notifyStatus(workflowName as WorkflowName, 'error', error)
    }

    onError?.(error as Error)
    throw error
  }
}

/**
 * 执行完整的工作流链路，确保数据正确传递
 * 这个函数应该在页面初始化时调用，确保所有依赖数据都已准备好
 */
export async function executeFullWorkflowChain(): Promise<void> {
  /** 第一步：执行 data_report 工作流 */
  const dataReportParams = getDataReportParameters()
  const dataReportData = await executeStreamRequest(
    'data_report',
    dataReportParams,
    () => {}, // data_report 不需要UI回调
  )

  /** 第二步：执行 insight_report 工作流 */
  const insightParams = getInsightReportParameters(dataReportData)
  const insightReportData = await executeStreamRequest(
    'insight_report',
    insightParams,
    () => {}, // insight_report 不需要UI回调
  )

  /** 第三步：执行 competitor_report 工作流 */
  const competitorParams = getCompetitorReportParameters(dataReportData, insightReportData)
  await executeStreamRequest(
    'competitor_report',
    competitorParams,
    () => {}, // competitor_report 不需要UI回调
  )
}

/**
 * 调用 Insight Report 流式 API - 只调用insight_report工作流
 * 注意：此函数假设data_report已经执行过，数据存储在localStorage中
 */
export async function callCozeStreamAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  try {
    /** 获取data_report的输出数据（从内存缓存） */
    let dataReportData = getWorkflowOutput('data_report')

    /** 如果没有data_report数据，先执行data_report */
    if (Object.keys(dataReportData).length === 0) {
      const dataReportParams = getDataReportParameters()

      /** data_report 开始执行 */

      dataReportData = await executeStreamRequest(
        'data_report',
        dataReportParams,
        () => {
          // data_report 不需要UI回调
        },
        (error) => {
          onError?.(error)
        },
      )

      /** data_report 完成 */
    }

    /** 执行 insight_report 工作流，立即开始流式输出 */
    const insightParams = getInsightReportParameters(dataReportData)
    await executeStreamRequest(
      'insight_report',
      insightParams,
      onData,
      onError,
      onComplete,
    )
  }
  catch (error) {
    onError?.(error as Error)
  }
}

/**
 * 调用 Competitor Report 流式 API - 只调用competitor_report工作流
 * 注意：此函数假设data_report和insight_report已经执行过，数据存储在localStorage中
 */
export async function callCompetitorCozeStreamAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  try {
    /** 获取已执行的工作流数据（从内存缓存） */
    let dataReportData = getWorkflowOutput('data_report')
    let insightReportData = getWorkflowOutput('insight_report')

    /** 如果缺少依赖数据，按顺序执行前置工作流 */
    if (Object.keys(dataReportData).length === 0) {
      const dataReportParams = getDataReportParameters()

      /** data_report 开始执行 */

      dataReportData = await executeStreamRequest(
        'data_report',
        dataReportParams,
        () => {
          // data_report 不需要UI回调
        },
        (error) => {
          onError?.(error)
        },
      )

      /** data_report 完成 */
    }

    if (Object.keys(insightReportData).length === 0) {
      const insightParams = getInsightReportParameters(dataReportData)
      insightReportData = await executeStreamRequest(
        'insight_report',
        insightParams,
        () => {}, // insight_report 不需要UI回调
        (error) => {
          onError?.(error)
        },
      )
    }

    /** 执行 competitor_report 工作流，立即开始流式输出 */
    const competitorParams = getCompetitorReportParameters(dataReportData, insightReportData)
    await executeStreamRequest(
      'competitor_report',
      competitorParams,
      onData,
      onError,
      () => {
        /** competitor_report 完成后，标记聚合 thinking1 数据流完成 */
        streamingDataManager.completeThinking1Stream()
        onComplete?.()
      },
    )
  }
  catch (error) {
    onError?.(error as Error)
  }
}

/**
 * 调用 duibiao_disassembly_text 流式 API - 采用统一的 cozeStreamApi 模式
 * 注意：此函数假设 distill_daren_list 和 data_report 已经执行过，数据存储在缓存中
 */
export async function callDuibiaoDisassemblyTextAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  try {
    /** 检查是否已有缓存数据，避免重复请求 */
    const duibiaoCache = getWorkflowOutput('duibiao_disassembly_text')
    const hasValidCache = duibiaoCache && (
      duibiaoCache['笔记title']
      || duibiaoCache.title
      || duibiaoCache['笔记正文']
      || duibiaoCache.content
    )

    if (hasValidCache) {
      console.log('[DuibiaoDisassemblyTextAPI] 防重：检测到有效缓存数据，跳过重复请求')
      console.log('[DuibiaoDisassemblyTextAPI] 缓存数据内容:', duibiaoCache)
      onComplete?.()
      return
    }

    /** 通知工作流开始 */
    workflowStatusManager.notifyStatus('duibiao_disassembly_text' as WorkflowName, 'started')

    /** 执行 duibiao_disassembly_text 工作流，立即开始流式输出 */
    const duibiaoParams = getDuibiaoDisassemblyTextParameters()
    await executeStreamRequest(
      'duibiao_disassembly_text',
      duibiaoParams,
      onData,
      onError,
      () => {
        /** duibiao_disassembly_text 完成后的回调 */
        workflowStatusManager.notifyStatus('duibiao_disassembly_text' as WorkflowName, 'completed')
        onComplete?.()
      },
    )
  }
  catch (error) {
    /** 通知工作流错误 */
    workflowStatusManager.notifyStatus('duibiao_disassembly_text' as WorkflowName, 'error', error)
    onError?.(error as Error)
  }
}

/**
 * 导出 getWorkflowOutput 和 setWorkflowOutput 函数供其他模块使用
 */
export { getWorkflowOutput, setWorkflowOutput }

/**
 * 调用 distill_daren_list 流式 API
 * 用于获取小红书达人列表数据
 */
export async function callDistillDarenListAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  taskInstanceId?: string,
): Promise<void> {
  try {
    /** 获取已执行的工作流数据 */
    const dataReportData = getWorkflowOutput('data_report')
    const competitorReportData = getWorkflowOutput('competitor_report')
    /** 从 localStorage 获取基础参数 */
    const dataReportParams = JSON.parse(localStorage.getItem('dataReportParams') || '{}')

    /** 构造 distill_daren_list 参数 */
    const parameters = {
      brand_report: dataReportData.brand_report || '',
      biji_data: dataReportData.biji_data || '',
      daren_data: dataReportData.daren_data || '',
      product_data: dataReportData.product_data || '',
      industry_name: dataReportParams.industry_name || competitorReportData.industry_name || '',
    }

    /** 获取 token 和 taskInstanceId */
    const token = await getUserToken()
    const instanceId = taskInstanceId || getTaskInstanceId()

    if (!instanceId) {
      throw new Error('未找到有效的 taskInstanceId')
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'

    /** 存储响应数据 */
    const responseData: Record<string, any> = {}
    /** 用于收集执行参数 */
    const extractedParams = { executionId: null as string | null, taskInstanceId: null as string | null }

    fetchEventSourceWithTimeout(`${apiUrl}/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: instanceId,
        platform: 'rednote',
        workflowName: 'distill_daren_list',
        parameters,
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        try {
          const parsed = JSON.parse(ev.data)

          /** 尝试提取执行参数 */
          if ((parsed.execution_id || parsed.executionId) && !extractedParams.executionId) {
            extractedParams.executionId = parsed.execution_id || parsed.executionId
          }
          if ((parsed.task_instance_id || parsed.taskInstanceId) && !extractedParams.taskInstanceId) {
            extractedParams.taskInstanceId = parsed.task_instance_id || parsed.taskInstanceId
          }

          /** 缓存工作流输出 */
          if (parsed.node_title && parsed.content) {
            responseData[parsed.node_title] = parsed.content

            /** 存储到全局缓存 */
            setWorkflowOutput('distill_daren_list', responseData)

            /** 特殊处理 biji_thinking 数据，推送到专用流 */
            if (parsed.node_title === 'biji_thinking') {
              streamingDataManager.pushOperationsThinking1Data(parsed.content)
            }
          }

          /** 转换为 ParsedStreamData 格式 */
          if (parsed.content) {
            onData({
              type: 'workflow_param',
              content: parsed.content,
              nodeTitle: parsed.node_title,
            })
          }
        }
        catch {
          /** 忽略解析错误 */
        }
      },
      onclose() {
        /** 最终保存到缓存 */
        setWorkflowOutput('distill_daren_list', responseData)

        /** 存储执行参数（如果提取到了） */
        if (extractedParams.executionId || extractedParams.taskInstanceId) {
          const reportType = 'distill_daren_list'
          const realExecutionId = extractedParams.executionId || `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          const realTaskInstanceId = extractedParams.taskInstanceId || instanceId

          /** 确保两个ID不同 */
          const finalExecutionId = realExecutionId === realTaskInstanceId
            ? `${realExecutionId}_exec`
            : realExecutionId

          setWorkflowExecutionParams(reportType, finalExecutionId, realTaskInstanceId)
        }

        /** 标记 biji_thinking 数据流完成 */
        streamingDataManager.completeOperationsThinking1Stream()

        /** 🎯 通过数据同步管理器通知数据准备完成 */
        setTimeout(() => {
          const { dataSyncManager } = require('./index').trendAg
          if (dataSyncManager) {
            dataSyncManager.markDataReady('distillDarenList', responseData)
            console.warn('[cozeStreamApi] distill_daren_list 数据准备完成，通知相关组件')
          }
        }, 50)

        onComplete?.()
      },
      onerror(error) {
        onError?.(error as Error)
      },
    })
  }
  catch (error) {
    onError?.(error as Error)
  }
}

/**
 * 调用 Planning Scheme 流式 API - 只调用planning_scheme工作流
 * 注意：此函数假设前置工作流已经执行过，数据存储在localStorage中
 */
export async function callPlanningSchemeCozeStreamAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  try {
    /** 获取已执行的工作流数据（从内存缓存） */
    let dataReportData = getWorkflowOutput('data_report')
    let insightReportData = getWorkflowOutput('insight_report')
    let competitorReportData = getWorkflowOutput('competitor_report')

    /** 如果缺少依赖数据，按顺序执行前置工作流 */
    if (Object.keys(dataReportData).length === 0) {
      const dataReportParams = getDataReportParameters()
      dataReportData = await executeStreamRequest(
        'data_report',
        dataReportParams,
        () => {}, // data_report 不需要UI回调
        (error) => {
          onError?.(error)
        },
      )
    }

    if (Object.keys(insightReportData).length === 0) {
      const insightParams = getInsightReportParameters(dataReportData)
      insightReportData = await executeStreamRequest(
        'insight_report',
        insightParams,
        () => {}, // insight_report 不需要UI回调
        (error) => {
          onError?.(error as Error)
        },
      )
    }

    if (Object.keys(competitorReportData).length === 0) {
      const competitorParams = getCompetitorReportParameters(dataReportData, insightReportData)
      competitorReportData = await executeStreamRequest(
        'competitor_report',
        competitorParams,
        () => {}, // competitor_report 不需要UI回调
        (error) => {
          onError?.(error as Error)
        },
      )
    }

    /** 执行 planning_scheme 工作流，立即开始流式输出 */
    const planningParams = getPlanningSchemeParameters(dataReportData, insightReportData, competitorReportData)

    await executeStreamRequest(
      'planning_scheme',
      planningParams,
      onData,
      onError,
      () => {
        /** planning_scheme 完成后，标记专用 thinking1 数据流完成 */
        streamingDataManager.completePlanningThinking1Stream()
        onComplete?.()
      },
    )
  }
  catch (error) {
    onError?.(error as Error)
  }
}

/**
 * 调用 original_work 流式 API
 * 用于生成原创内容和图片
 */
export async function callOriginalWorkAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  taskInstanceId?: string,
  extraParams?: {
    selectedTopicId?: string
    selectedRednoteId?: string
    planningReport?: string
    userSubmitPic?: string
  },
): Promise<void> {
  try {
    /** 从 store 获取数据进行防重检查 */
    const { trendAg } = await import('./index')
    const storeInstance = trendAg.stateStore

    /** 防重检查：如果正在请求中或已触发过，直接返回 */
    if (storeInstance.originalWorkRequesting || storeInstance.hasTriggeredOriginalWork) {
      return
    }

    /** 标记请求开始 */
    storeInstance.originalWorkRequesting = true
    storeInstance.hasTriggeredOriginalWork = true

    /** 清空图片预览缓存，确保使用最新工作流数据 */
    localStorage.removeItem('imagePreviewData')
    console.log('[callOriginalWorkAPI] 已清空图片预览缓存，确保使用最新工作流数据')

    /** 获取已执行的工作流数据 */
    const planningSchemeData = getWorkflowOutput('planning_scheme')
    const hotpotsData = getWorkflowOutput('hotpots_analysis')

    /** 构建参数 - any_topic 应该是所有 topic details 的拼接 */
    let anyTopic = ''

    /** 获取所有 topic details 并拼接 */
    if (storeInstance?.hotpotsTopics) {
      const topicDetails = []

      /** 收集所有的 topic_detail */
      for (let i = 1; i <= 5; i++) {
        const detailKey = `topic${i}_detail` as keyof typeof storeInstance.hotpotsTopics
        if (storeInstance.hotpotsTopics[detailKey]) {
          topicDetails.push(storeInstance.hotpotsTopics[detailKey])
        }
      }

      /** 拼接所有 details */
      anyTopic = topicDetails.join('\n\n')
    }
    /** 如果 store 中没有，从缓存中获取 */
    else if (hotpotsData) {
      const topicDetails = []
      for (let i = 1; i <= 5; i++) {
        const detailKey = `topic${i}_detail`
        if (hotpotsData[detailKey]) {
          topicDetails.push(hotpotsData[detailKey])
        }
      }
      anyTopic = topicDetails.join('\n\n')
    }

    /** 获取 planning_report */
    let planningReport = ''
    if (extraParams?.planningReport) {
      /** 优先使用传入的参数 */
      planningReport = extraParams.planningReport
    }
    else if (storeInstance?.planningReportContent) {
      /** 从 store 中获取 */
      planningReport = storeInstance.planningReportContent
    }
    else if (planningSchemeData.planning_report) {
      /** 从缓存中获取 */
      planningReport = planningSchemeData.planning_report
    }

    /** 获取用户提交的图片 */
    let userSubmitPic = ''
    if (extraParams?.userSubmitPic) {
      /** 优先使用传入的参数 */
      userSubmitPic = extraParams.userSubmitPic
    }
    else if (storeInstance?.userSubmitPic) {
      /** 从 store 中获取默认图片 */
      userSubmitPic = storeInstance.userSubmitPic
    }
    else if (extraParams?.selectedRednoteId) {
      /** 如果选择了小红书笔记，使用笔记ID（后续可以改为获取笔记的实际图片URL） */
      userSubmitPic = extraParams.selectedRednoteId
    }

    const parameters = {
      planning_report: planningReport,
      any_topic: anyTopic,
      user_submit_pic: userSubmitPic,
    }

    /** 获取 token 和 taskInstanceId */
    const token = await getUserToken()
    const instanceId = taskInstanceId || getTaskInstanceId()

    if (!instanceId) {
      throw new Error('未找到有效的 taskInstanceId')
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'

    /** 存储响应数据 */
    const responseData: Record<string, any> = {}
    /** 存储分片数据，按节点分组 */
    const nodeFragments: Record<string, { seq: number, text: string }[]> = {}

    fetchEventSourceWithTimeout(`${apiUrl}/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: instanceId,
        platform: 'rednote',
        workflowName: 'original_work',
        parameters,
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        /** 处理 meta 事件，提取 executionId 和 taskInstanceId */
        if (ev.event === 'meta') {
          try {
            const metaData = JSON.parse(ev.data)

            if (metaData.executionId && metaData.taskInstanceId) {
              /** 存储执行参数以供后续使用 */
              setWorkflowExecutionParams('original_work', metaData.executionId, metaData.taskInstanceId)
            }
          }
          catch {
            /** 忽略解析错误 */
          }
          return
        }

        try {
          const parsed = JSON.parse(ev.data)

          /** 处理分片数据 */
          if (parsed.node_title && parsed.content !== undefined) {
            const nodeTitle = parsed.node_title
            const seqId = Number.parseInt(parsed.node_seq_id) || 0

            /** 初始化节点分片数组 */
            if (!nodeFragments[nodeTitle]) {
              nodeFragments[nodeTitle] = []
            }

            /** 添加分片 */
            if (parsed.content) {
              nodeFragments[nodeTitle].push({ seq: seqId, text: parsed.content })
            }

            /** 如果节点完成，拼接所有分片 */
            if (parsed.node_is_finish) {
              nodeFragments[nodeTitle].sort((a, b) => a.seq - b.seq)
              const fullContent = nodeFragments[nodeTitle].map(p => p.text).join('')

              /** 特殊处理image节点，确保完全解析为数组 */
              let processedContent: string | any[] = fullContent
              if (nodeTitle === 'image' && fullContent) {
                try {
                  /** 使用通用解码函数处理HTML实体和转义字符 */
                  const decodedString = decodeHtmlEntitiesAndEscapes(fullContent)
                  console.log('[cozeStreamApi] image节点原始数据:', fullContent)
                  console.log('[cozeStreamApi] image节点解码后:', decodedString)

                  /** 测试用例验证 */
                  if (fullContent.includes('[\\\"') || fullContent.includes('&quot;')) {
                    console.log('[cozeStreamApi] 检测到转义/HTML实体编码，应用解码处理')
                  }

                  /** 尝试解析JSON数组 */
                  const parsedImages = JSON.parse(decodedString)
                  if (Array.isArray(parsedImages) && parsedImages.length > 0) {
                    /** 确保所有URL都是有效的字符串 */
                    const validImages = parsedImages.filter(img =>
                      typeof img === 'string' && img.trim().length > 0,
                    )
                    if (validImages.length > 0) {
                      processedContent = validImages
                      console.log('[cozeStreamApi] image节点成功解析为数组:', validImages)
                    }
                  }
                }
                catch (error) {
                  console.error('[cozeStreamApi] image节点JSON解析失败:', error)
                  console.error('[cozeStreamApi] 原始数据:', fullContent)
                  console.error('[cozeStreamApi] 解码后数据:', decodedString)

                  /** 解析失败时，尝试作为单个URL处理 */
                  if (fullContent.includes('http')) {
                    /** 如果包含http但解析失败，可能是格式问题，尝试从字符串中提取URL */
                    const urlMatch = fullContent.match(/https?:\/\/[^\s"'\]]+/g)
                    if (urlMatch && urlMatch.length > 0) {
                      processedContent = urlMatch
                      console.log('[cozeStreamApi] 通过正则提取到图片URL:', urlMatch)
                    }
                    else {
                      processedContent = [fullContent]
                      console.warn('[cozeStreamApi] 无法提取URL，保持原始字符串')
                    }
                  }
                }
              }

              responseData[nodeTitle] = processedContent

              /** 存储到全局缓存 */
              const originalWorkData = getWorkflowOutput('original_work')
              const updatedOriginalWorkData = { ...originalWorkData, [nodeTitle]: processedContent }
              setWorkflowOutput('original_work', updatedOriginalWorkData)

              /** 发送完整内容 */
              onData({
                type: 'workflow_param',
                content: Array.isArray(processedContent)
                  ? JSON.stringify(processedContent)
                  : fullContent,
                nodeTitle,
              })
            }
          }
        }
        catch {
          /** 忽略解析错误 */
        }
      },
      onclose() {
        /** 重置请求状态 */
        storeInstance.originalWorkRequesting = false
        onComplete?.()
      },
      onerror(error) {
        /** 检查是否是页面可见性变化导致的连接中断 */
        const isPageHidden = document.hidden || document.visibilityState === 'hidden'

        if (isPageHidden) {
          /** 只重置请求状态，保持已触发标记 */
          storeInstance.originalWorkRequesting = false
        }
        else {
          /** 真实错误，重置所有状态 */
          storeInstance.originalWorkRequesting = false
          storeInstance.hasTriggeredOriginalWork = false
        }

        onError?.(error as Error)
      },
    })
  }
  catch (error) {
    /** 重置请求状态 */
    const { trendAg } = await import('./index')
    trendAg.stateStore.originalWorkRequesting = false
    onError?.(error as Error)
  }
}

/**
 * 调用 hotpots_analysis 流式 API
 * 用于获取热点分析数据
 */
export async function callHotpotsAnalysisAPI(
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  parameters?: {
    planning_report: string
  },
  taskInstanceId?: string,
): Promise<void> {
  try {
    /** 从 store 获取数据进行防重检查 */
    const { trendAg } = await import('./index')
    const storeInstance = trendAg.stateStore

    /** 防重检查：如果正在请求中，直接返回 */
    if (storeInstance.hotpotsAnalysisRequesting) {
      return
    }

    /** 标记请求开始 */
    storeInstance.hotpotsAnalysisRequesting = true
    storeInstance.hasTriggeredHotpotsAnalysis = true

    /** 获取 token 和 taskInstanceId */
    const token = await getUserToken()
    const instanceId = taskInstanceId || getTaskInstanceId()

    if (!instanceId) {
      throw new Error('未找到有效的 taskInstanceId')
    }

    if (!parameters?.planning_report) {
      throw new Error('hotpots_analysis 缺少必要参数 planning_report')
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'

    /** 存储响应数据 */
    const responseData: Record<string, any> = {}

    fetchEventSourceWithTimeout(`${apiUrl}/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: instanceId,
        platform: 'rednote',
        workflowName: 'hotpots_analysis',
        parameters,
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        try {
          const parsed = JSON.parse(ev.data)

          /** 缓存工作流输出 */
          if (parsed.node_title && parsed.content) {
            responseData[parsed.node_title] = parsed.content

            /** 存储到全局缓存 */
            setWorkflowOutput('hotpots_analysis', responseData)
          }

          /** 转换为 ParsedStreamData 格式 */
          if (parsed.content) {
            onData({
              type: 'workflow_param',
              content: parsed.content,
              nodeTitle: parsed.node_title,
            })
          }
        }
        catch {
          /** 忽略解析错误 */
        }
      },
      onclose() {
        /** 最终保存到缓存 */
        setWorkflowOutput('hotpots_analysis', responseData)
        /** 重置请求状态 */
        storeInstance.hotpotsAnalysisRequesting = false
        onComplete?.()
      },
      onerror(error) {
        /** 重置请求状态 */
        storeInstance.hotpotsAnalysisRequesting = false
        onError?.(error as Error)
      },
    })
  }
  catch (error) {
    /** 重置请求状态 */
    const { trendAg } = await import('./index')
    trendAg.stateStore.hotpotsAnalysisRequesting = false
    onError?.(error as Error)
  }
}
/**
 * 可视化流式请求缓存
 * 用于存储可视化请求的结果，避免重复请求
 */
const visualizationCache = new Map<string, {
  data: string
  timestamp: number
  loading: boolean
}>()

/**
 * 清理过期的可视化缓存（超过30分钟）
 */
function cleanExpiredVisualizationCache(): void {
  const now = Date.now()
  const expireTime = 30 * 60 * 1000 // 30分钟

  visualizationCache.forEach((value, key) => {
    if (now - value.timestamp > expireTime) {
      visualizationCache.delete(key)
    }
  })
}

/**
 * 从可视化数据中提取纯HTML内容
 * 去除非HTML文字部分，只保留HTML标签和内容
 */
export function extractHtmlFromVisualizationData(data: string): string {
  if (!data)
    return ''

  /** 移除markdown代码块标记 */
  let cleanData = data.replace(/```html\s*/g, '').replace(/```\s*/g, '')

  /** 查找HTML内容的开始和结束 */
  const htmlStartPattern = /<(!DOCTYPE html|html|HTML)/i
  const htmlEndPattern = /<\/html>/i

  const startMatch = cleanData.match(htmlStartPattern)
  const endMatch = cleanData.match(htmlEndPattern)

  if (startMatch && endMatch) {
    const startIndex = startMatch.index || 0
    const endIndex = (endMatch.index || 0) + endMatch[0].length
    cleanData = cleanData.substring(startIndex, endIndex)
  }
  else {
    /** 如果没有完整的HTML结构，查找所有HTML标签内容 */
    const htmlTagPattern = /<[^>]+>[\s\S]*?<\/[^>]+>/g
    const htmlMatches = cleanData.match(htmlTagPattern)
    if (htmlMatches) {
      cleanData = htmlMatches.join('\n')
    }
  }

  /** 清理多余的空白字符 */
  cleanData = cleanData.replace(/\n\s*\n/g, '\n').trim()

  return cleanData
}

/**
 * 检查工作流是否完成加载
 */
export function isWorkflowCompleted(workflowName: string): boolean {
  const workflowData = getWorkflowOutput(workflowName)
  return Object.keys(workflowData).length > 0
}

/**
 * 独立的可视化按钮状态管理，避免影响主流程
 */
let visualizationButtonEnabledState = false

/**
 * 设置可视化按钮启用状态（仅用于可视化功能）
 */
export function setVisualizationButtonEnabled(enabled: boolean): void {
  visualizationButtonEnabledState = enabled
}

/**
 * 检查competitor_report是否完成加载
 * 这是可视化按钮启用的前提条件
 */
export function isCompetitorReportCompleted(): boolean {
  /** 优先使用独立的可视化按钮状态，避免影响主流程 */
  return visualizationButtonEnabledState || isWorkflowCompleted('competitor_report')
}

/**
 * 原有的competitor_report完成状态检查（保持主流程不变）
 */
export function isCompetitorReportCompletedForMainFlow(): boolean {
  return isWorkflowCompleted('competitor_report')
}

/**
 * 基于工作流状态和映射关系识别报告类型
 * 这是更可靠的识别方案，不依赖于不稳定的标题文本
 */
function getReportTypeFromWorkflowState(reportItem: any, mapperStore?: any): string {
  /** 方法1: 通过工作流输出缓存识别 */
  const workflowTypes = ['insight_report', 'competitor_report', 'planning_scheme']

  for (const workflowType of workflowTypes) {
    const workflowData = getWorkflowOutput(workflowType)
    if (Object.keys(workflowData).length > 0) {
      /** 检查当前报告内容是否来自此工作流 */
      const reportFields = workflowType === 'planning_scheme'
        ? ['planning_report']
        : [workflowType]

      for (const field of reportFields) {
        if (workflowData[field] && reportItem?.content
          && workflowData[field].includes(reportItem.content.substring(0, 100))) {
          return workflowType === 'planning_scheme'
            ? 'planning_report'
            : workflowType
        }
      }
    }
  }

  /** 方法2: 通过 mapperStore 的反向查找 */
  if (reportItem?.id && mapperStore) {
    try {
      /** 查找哪个 node_title 映射到当前报告ID */
      for (const [nodeTitle, reportId] of Object.entries(mapperStore.nodeTitleToReportId || {})) {
        if (reportId === reportItem.id) {
          /** 基于 node_title 确定报告类型 */
          if (nodeTitle.includes('insight') || nodeTitle.includes('research_content')) {
            return 'insight_report'
          }
          if (nodeTitle.includes('competitor') || nodeTitle.includes('research_content_1')) {
            return 'competitor_report'
          }
          if (nodeTitle.includes('planning') || nodeTitle.includes('scheme')) {
            return 'planning_report'
          }
        }
      }
    }
    catch {
      /** 忽略错误 */
    }
  }

  /** 方法3: 基于 meta.step 和内容特征的组合判断 */
  if (reportItem?.meta?.step) {
    const step = reportItem.meta.step
    const content = reportItem.content || ''

    /** 不同步骤通常对应不同类型的报告 */
    if (step === 'step1' || step === 'step0') {
      if (content.includes('竞品') || content.includes('competitor') || content.includes('competitive')) {
        return 'competitor_report'
      }
      if (content.includes('策划') || content.includes('planning') || content.includes('marketing')) {
        return 'planning_report'
      }
      return 'insight_report' // 默认为洞察报告
    }
  }

  /** 方法4: 作为最后的备选方案，使用标题匹配（但优先级最低） */
  return getReportTypeFromTitleFallback(reportItem?.title || '')
}

/**
 * 基于标题的备选识别方法（仅作为最后的备选方案）
 */
function getReportTypeFromTitleFallback(reportTitle: string): string {
  if (!reportTitle)
    return 'unknown'

  const titleLower = reportTitle.toLowerCase()

  if (titleLower.includes('insight') || titleLower.includes('洞察') || titleLower.includes('分析报告')) {
    return 'insight_report'
  }
  if (titleLower.includes('planning') || titleLower.includes('scheme') || titleLower.includes('策划') || titleLower.includes('营销') || titleLower.includes('marketing')) {
    return 'planning_report'
  }
  if (titleLower.includes('competitor') || titleLower.includes('competitive') || titleLower.includes('竞品') || titleLower.includes('竞争') || titleLower.includes('对手')) {
    return 'competitor_report'
  }

  return 'unknown'
}

/**
 * 获取报告内容的辅助函数
 * 根据报告类型动态获取对应的报告内容
 * 增强版：支持多种获取方式和内容来源，使用更可靠的报告类型识别
 */
function getReportContentByState(reportTitle: string, reportItem?: any, mapperStore?: any): { content: string, reportType: string } {
  /** 清理过期缓存 */
  cleanExpiredVisualizationCache()

  /** 使用更可靠的报告类型识别方法 */
  const reportType = reportItem
    ? getReportTypeFromWorkflowState(reportItem, mapperStore)
    : getReportTypeFromTitleFallback(reportTitle)

  /** 方法1: 基于标准工作流名称获取 */
  if (reportType === 'insight_report') {
    const insightData = getWorkflowOutput('insight_report')
    const reportContent = insightData.insight_report || ''
    if (reportContent) {
      return { content: reportContent, reportType }
    }
  }

  if (reportType === 'planning_report') {
    const planningData = getWorkflowOutput('planning_scheme')
    const reportContent = planningData.planning_report || ''
    if (reportContent) {
      return { content: reportContent, reportType }
    }
  }

  if (reportType === 'competitor_report') {
    const competitorData = getWorkflowOutput('competitor_report')
    const reportContent = competitorData.competitor_report || ''
    if (reportContent) {
      return { content: reportContent, reportType }
    }
  }

  /** 方法2: 遍历所有工作流缓存，寻找可用的报告内容 */
  const allWorkflows = ['data_report', 'insight_report', 'competitor_report', 'planning_scheme']

  for (const workflowName of allWorkflows) {
    const workflowData = getWorkflowOutput(workflowName)

    /** 查找包含报告内容的字段 */
    const reportFields = ['insight_report', 'competitor_report', 'planning_report']
    for (const field of reportFields) {
      if (workflowData[field] && typeof workflowData[field] === 'string' && workflowData[field].length > 100) {
        return { content: workflowData[field], reportType }
      }
    }
  }

  /** 方法3: 如果是动态title，尝试从当前传入的内容中提取 */
  if (reportTitle && reportTitle.length > 100) {
    return { content: reportTitle, reportType }
  }

  return { content: '', reportType }
}

/**
 * 调用可视化流式 API
 * 用于将报告内容转换为HTML可视化图表
 * 修复版：使用报告类型作为缓存键，确保不同报告类型的可视化结果不会互相干扰
 */
export async function requestVisualizationStream(
  reportTitle: string,
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  taskInstanceId?: string,
  reportItem?: any, // 新增参数：传入完整的报告项用于更准确的类型识别
  mapperStore?: any, // 新增参数：传入 mapperStore 用于报告类型识别
): Promise<void> {
  try {
    /** 动态获取报告内容和类型，传入报告项以获得更准确的识别 */
    const reportResult = getReportContentByState(reportTitle, reportItem, mapperStore)
    const { content: anyReport, reportType } = reportResult

    if (!anyReport) {
      const error = new Error(`未找到 ${reportTitle} 的报告内容，请确保相关工作流已完成`)
      onError?.(error)
      return
    }

    /** 生成基于报告类型的缓存键，确保不同报告类型使用不同缓存 */
    const currentInstanceId = taskInstanceId || getTaskInstanceId()
    const cacheKey = `${reportType}_${currentInstanceId}`

    /** 检查缓存 */
    const cached = visualizationCache.get(cacheKey)
    if (cached && !cached.loading) {
      onData({
        type: 'workflow_param',
        content: cached.data,
        nodeTitle: 'visualization_result',
      })
      onComplete?.()
      return
    }

    /** 如果正在加载，直接返回 */
    if (cached?.loading) {
      return
    }

    /** 通知可视化工作流开始 */
    workflowStatusManager.notifyStatus('visualization', 'started')

    /** 设置加载状态 */
    visualizationCache.set(cacheKey, {
      data: '',
      timestamp: Date.now(),
      loading: true,
    })

    /** 获取对应报告类型的执行参数 */
    const executionParams = getWorkflowExecutionParams(reportType)
    if (!executionParams) {
      const error = new Error(`未找到 ${reportType} 的执行参数，请确保相关工作流已完成`)
      visualizationCache.delete(cacheKey)
      onError?.(error)
      return
    }

    /** 构造新的请求参数格式 */
    const parameters = {
      any_report: anyReport,
    }

    /** 获取 token 和当前会话的 taskInstanceId */
    const token = await getUserToken()
    const currentTaskInstanceId = getTaskInstanceId()

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
    const fullUrl = `${apiUrl}/app/market/stream/execute-main`

    /** 存储响应数据 */
    let visualizationResult = ''

    fetchEventSourceWithTimeout(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: currentTaskInstanceId,
        workflowName: 'visualization',
        baseReportExecutionId: executionParams.executionId,
        parameters,
        platform: 'rednote',
      }),
      async onopen(_response) {
        /** 连接建立 */
      },
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        try {
          const parsed = JSON.parse(ev.data)

          /** 收集可视化结果 */
          if (parsed.content) {
            visualizationResult += parsed.content

            /** 转换为 ParsedStreamData 格式并回调 */
            onData({
              type: 'workflow_param',
              content: parsed.content,
              nodeTitle: parsed.node_title || 'visualization_result',
            })
          }
        }
        catch {
          /** 忽略解析错误 */
        }
      },
      onclose() {
        /** 更新缓存 */
        visualizationCache.set(cacheKey, {
          data: visualizationResult,
          timestamp: Date.now(),
          loading: false,
        })

        /** 通知可视化工作流完成 */
        workflowStatusManager.notifyStatus('visualization', 'completed')

        onComplete?.()
      },
      onerror(error) {
        visualizationCache.delete(cacheKey) // 清理失败的缓存

        /** 通知可视化工作流错误 */
        workflowStatusManager.notifyStatus('visualization', 'error', error)

        onError?.(error as Error)
      },
    })
  }
  catch (error) {
    const cacheKey = `${reportTitle}_${taskInstanceId || getTaskInstanceId()}`
    visualizationCache.delete(cacheKey) // 清理失败的缓存
    onError?.(error as Error)
  }
}

/**
 * 获取可视化缓存状态 - 用于调试
 */
export function getVisualizationCacheStatus(): Record<string, { hasData: boolean, timestamp: number, loading: boolean }> {
  const status: Record<string, { hasData: boolean, timestamp: number, loading: boolean }> = {}
  visualizationCache.forEach((value, key) => {
    status[key] = {
      hasData: !!value.data,
      timestamp: value.timestamp,
      loading: value.loading,
    }
  })
  return status
}

/**
 * 调试函数：在控制台打印缓存状态
 */
export function debugVisualizationCache(): void {
  const _status = getVisualizationCacheStatus()
  /** 调试函数，用于开发环境检查缓存状态 */
}

/** 开发环境下暴露调试函数到全局 */
if (import.meta.env.DEV) {
  (window as any).debugVisualizationCache = debugVisualizationCache
}

/**
 * 清理可视化缓存
 */
export function clearVisualizationCache(): void {
  visualizationCache.clear()
}

/**
 * 获取特定报告类型的可视化缓存
 * 用于检查是否已有缓存数据
 */
export function getVisualizationCache(reportType: string, taskInstanceId?: string): { data: string, timestamp: number } | null {
  const instanceId = taskInstanceId || getTaskInstanceId()
  const cacheKey = `${reportType}_${instanceId}`

  const cached = visualizationCache.get(cacheKey)
  if (cached && !cached.loading && cached.data) {
    return { data: cached.data, timestamp: cached.timestamp }
  }

  return null
}

/**
 * 清理特定报告类型的可视化缓存
 * 用于报告类型切换时清理旧缓存
 */
export function clearVisualizationCacheByReportType(reportType: string, taskInstanceId?: string): void {
  const instanceId = taskInstanceId || getTaskInstanceId()
  const cacheKey = `${reportType}_${instanceId}`

  if (visualizationCache.has(cacheKey)) {
    visualizationCache.delete(cacheKey)
  }
}

/**
 * 修改历史跟踪
 * 用于记录每个报告类型的修改次数和历史
 */
const modificationHistory = new Map<string, {
  count: number
  lastModified: number
  originalBackup?: string
}>()

/**
 * 获取报告修改历史信息
 */
export function getModificationHistory(reportType: string): { count: number, lastModified: number } {
  const history = modificationHistory.get(reportType)
  return history
    ? { count: history.count, lastModified: history.lastModified }
    : { count: 0, lastModified: 0 }
}

/**
 * 记录报告修改历史
 */
function recordModificationHistory(reportType: string, originalContent?: string): void {
  const existing = modificationHistory.get(reportType)
  const newHistory = {
    count: (existing?.count || 0) + 1,
    lastModified: Date.now(),
    originalBackup: existing?.originalBackup || originalContent,
  }
  modificationHistory.set(reportType, newHistory)
}

/**
 * 检查是否有未保存的修改
 */
export function hasUnsavedModifications(reportType?: string): boolean {
  const modifyReportData = getWorkflowOutput('modify_report')

  if (!reportType) {
    /** 检查是否有任何未保存的修改 */
    return modifyReportData.modification_status === 'modified' && !modifyReportData.is_saved
  }

  /** 检查特定报告类型的未保存修改 */
  return modifyReportData.target_report_type === reportType
    && modifyReportData.modification_status === 'modified'
    && !modifyReportData.is_saved
}

/**
 * 获取修改状态信息
 */
export function getModificationStatus(reportType: string): {
  hasModifications: boolean
  isSaved: boolean
  modifiedAt: number
  modificationId: string | null
} {
  const modifyReportData = getWorkflowOutput('modify_report')

  const hasModifications = modifyReportData.target_report_type === reportType
    && modifyReportData.modification_status === 'modified'

  return {
    hasModifications,
    isSaved: modifyReportData.is_saved || false,
    modifiedAt: modifyReportData.modified_at || 0,
    modificationId: modifyReportData.modification_id || null,
  }
}

/**
 * 标记修改为已保存
 */
function markModificationAsSaved(reportType: string): void {
  const modifyReportData = getWorkflowOutput('modify_report')

  if (modifyReportData.target_report_type === reportType) {
    const updatedData = {
      ...modifyReportData,
      modification_status: 'saved' as const,
      is_saved: true,
      saved_at: Date.now(),
    }

    setWorkflowOutput('modify_report', updatedData)
  }
}

/**
 * 清除未保存的修改
 */
export function clearUnsavedModifications(reportType: string): void {
  const modifyReportData = getWorkflowOutput('modify_report')

  if (modifyReportData.target_report_type === reportType && !modifyReportData.is_saved) {
    /** 清除modify_report缓存 */
    setWorkflowOutput('modify_report', {})
  }
}

/**
 * 获取修改后的报告内容（用于预览）
 */
export function getModifiedReportContent(reportType: string): string | null {
  const modifyReportData = getWorkflowOutput('modify_report')

  if (modifyReportData.target_report_type === reportType
    && modifyReportData.modified_report
    && typeof modifyReportData.modified_report === 'string') {
    return modifyReportData.modified_report
  }

  return null
}

/**
 * 将修改结果覆盖到原始报告缓存
 * 只有在保存成功后才调用此函数
 */
function applyModificationToOriginalReport(reportType: string): boolean {
  const modifyReportData = getWorkflowOutput('modify_report')

  if (!modifyReportData.modified_report
    || typeof modifyReportData.modified_report !== 'string'
    || modifyReportData.modified_report.length < 100) {
    return false
  }

  try {
    /** 根据报告类型覆盖对应的原始数据 */
    if (reportType === 'insight_report') {
      const insightData = getWorkflowOutput('insight_report')
      const updatedInsightData = { ...insightData, insight_report: modifyReportData.modified_report }
      setWorkflowOutput('insight_report', updatedInsightData)
    }
    else if (reportType === 'competitor_report') {
      const competitorData = getWorkflowOutput('competitor_report')
      const updatedCompetitorData = { ...competitorData, competitor_report: modifyReportData.modified_report }
      setWorkflowOutput('competitor_report', updatedCompetitorData)
    }
    else if (reportType === 'planning_report') {
      const planningData = getWorkflowOutput('planning_scheme')
      const updatedPlanningData = { ...planningData, planning_report: modifyReportData.modified_report }
      setWorkflowOutput('planning_scheme', updatedPlanningData)
    }
    else {
      return false
    }

    return true
  }
  catch {
    return false
  }
}

/**
 * 保存修改后的报告
 * 调用确认接口并在成功后覆盖原始报告数据
 */
export async function saveModifiedReport(reportType: string): Promise<boolean> {
  try {
    /** 检查是否有未保存的修改 */
    if (!hasUnsavedModifications(reportType)) {
      return false
    }

    /** 获取修改状态信息 */
    const modificationStatus = getModificationStatus(reportType)
    if (!modificationStatus.modificationId) {
      return false
    }

    /** 获取必要的参数 */
    const token = await getUserToken()
    const taskInstanceId = getTaskInstanceId()

    if (!taskInstanceId) {
      return false
    }

    /** 调用确认接口 */
    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
    const confirmUrl = `${apiUrl}/app/market/confirm-report-modify`

    const requestBody = {
      taskInstanceId,
      modificationId: modificationStatus.modificationId,
      confirmReason: '',
    }

    const response = await fetch(confirmUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestBody),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    /** 检查响应结果 */
    if (result.success && result.data?.confirmStatus === 'SUCCESS') {
      /** 将修改结果覆盖到原始报告缓存 */
      const applySuccess = applyModificationToOriginalReport(reportType)

      if (applySuccess) {
        /** 标记为已保存 */
        markModificationAsSaved(reportType)
        return true
      }
      else {
        return false
      }
    }
    else {
      return false
    }
  }
  catch {
    return false
  }
}

/**
 * 获取当前报告内容用于修改工作流
 * 参考 visualization 工作流的实现
 *
 * 修改逻辑：
 * - 第一次修改：使用原始报告数据
 * - 第二次及后续修改：使用上一次修改的 modified_report 数据
 */
function getAnyReportForModification(reportType: string): string {
  /** 优先检查是否有修改后的版本（modify_report 的 modified_report 数据） */
  const modifyReportData = getWorkflowOutput('modify_report')
  if (modifyReportData.modified_report && typeof modifyReportData.modified_report === 'string' && modifyReportData.modified_report.length > 100) {
    return modifyReportData.modified_report
  }

  /** 基于报告类型获取对应的原始报告内容 */
  if (reportType === 'insight_report') {
    const insightData = getWorkflowOutput('insight_report')
    const reportContent = insightData.insight_report || ''
    if (reportContent) {
      return reportContent
    }
  }

  if (reportType === 'competitor_report') {
    const competitorData = getWorkflowOutput('competitor_report')
    const reportContent = competitorData.competitor_report || ''
    if (reportContent) {
      return reportContent
    }
  }

  if (reportType === 'planning_report') {
    const planningData = getWorkflowOutput('planning_scheme')
    const reportContent = planningData.planning_report || ''
    if (reportContent) {
      return reportContent
    }
  }

  /** 如果没有找到特定类型的报告，尝试遍历所有工作流缓存 */
  const allWorkflows = ['data_report', 'insight_report', 'competitor_report', 'planning_scheme']

  for (const workflowName of allWorkflows) {
    const workflowData = getWorkflowOutput(workflowName)

    /** 查找包含报告内容的字段 */
    const reportFields = ['insight_report', 'planning_report', 'competitor_report']
    for (const field of reportFields) {
      if (workflowData[field] && typeof workflowData[field] === 'string' && workflowData[field].length > 100) {
        return workflowData[field]
      }
    }
  }

  return ''
}

/**
 * 调用 modify_report 工作流
 * 用于修改现有报告内容
 */
export async function callModifyReportAPI(
  reportType: string,
  modifyDemand: string,
  onData: (data: ParsedStreamData) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
  _taskInstanceId?: string,
): Promise<void> {
  try {
    /** 通知修改报告工作流开始 */
    workflowStatusManager.notifyStatus('modify_report', 'started')

    /** 获取当前报告内容 */
    const anyReport = getAnyReportForModification(reportType)

    if (!anyReport) {
      const error = new Error(`未找到 ${reportType} 的报告内容`)
      onError?.(error)
      return
    }

    /** 记录修改历史（在第一次修改时备份原始内容） */
    const history = getModificationHistory(reportType)
    if (history.count === 0) {
      recordModificationHistory(reportType, anyReport)
    }
    else {
      recordModificationHistory(reportType)
    }

    /** 获取对应报告类型的执行参数 */
    const executionParams = getWorkflowExecutionParams(reportType)
    if (!executionParams) {
      const error = new Error(`未找到 ${reportType} 的执行参数，请确保相关工作流已完成`)
      onError?.(error)
      return
    }

    /** 获取 token 和当前会话的 taskInstanceId */
    const token = await getUserToken()
    const currentTaskInstanceId = getTaskInstanceId()

    /** 构造新的 modify_report 参数格式 */
    const parameters = {
      any_report: anyReport,
      modify_demand: modifyDemand,
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
    const fullUrl = `${apiUrl}/app/market/stream/execute-main`

    /** 使用 fetchEventSource 调用 modify_report 工作流 */

    /** 存储响应数据 */
    const responseData: Record<string, string> = {}
    /** 用于收集执行参数 */
    const extractedParams = { executionId: null as string | null, taskInstanceId: null as string | null }

    fetchEventSourceWithTimeout(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: currentTaskInstanceId,
        workflowName: 'modify_report',
        baseReportExecutionId: executionParams.executionId,
        modificationReason: modifyDemand,
        parameters,
        platform: 'rednote',
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        try {
          const parsed = JSON.parse(ev.data)

          /** 尝试提取执行参数 */
          if ((parsed.execution_id || parsed.executionId) && !extractedParams.executionId) {
            extractedParams.executionId = parsed.execution_id || parsed.executionId
          }
          if ((parsed.task_instance_id || parsed.taskInstanceId) && !extractedParams.taskInstanceId) {
            extractedParams.taskInstanceId = parsed.task_instance_id || parsed.taskInstanceId
          }

          /** 缓存工作流输出 */
          if (parsed.node_title && parsed.content) {
            /** 对于 modified_report 节点，需要累积拼接内容 */
            if (parsed.node_title === 'modified_report') {
              if (!responseData[parsed.node_title]) {
                responseData[parsed.node_title] = ''
              }
              responseData[parsed.node_title] += parsed.content
            }
            else {
              responseData[parsed.node_title] = parsed.content
            }

            /** 存储到全局缓存 */
            setWorkflowOutput('modify_report', responseData)
          }

          /** 转换为 ParsedStreamData 格式 */
          if (parsed.content) {
            onData({
              type: 'workflow_param',
              content: parsed.content,
              nodeTitle: parsed.node_title,
            })
          }
        }
        catch {
          /** 忽略解析错误 */
        }
      },
      onclose() {
        /** 存储执行参数（如果提取到了） */
        if (extractedParams.executionId || extractedParams.taskInstanceId) {
          const realExecutionId = extractedParams.executionId || `exec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          const realTaskInstanceId = extractedParams.taskInstanceId || currentTaskInstanceId

          /** 确保两个ID不同 */
          const finalExecutionId = realExecutionId === realTaskInstanceId
            ? `${realExecutionId}_exec`
            : realExecutionId

          setWorkflowExecutionParams('modify_report', finalExecutionId, realTaskInstanceId)
        }

        /** 扩展缓存结构，添加状态和元数据 */
        const modifyReportCache = {
          ...responseData,
          modification_status: 'modified' as const, // 标记为已修改但未保存
          target_report_type: reportType,
          modified_at: Date.now(),
          is_saved: false,
          modification_id: extractedParams.executionId || null, // 保存executionId用于后续确认接口
        }

        /** 最终保存到缓存（包含状态信息） */
        setWorkflowOutput('modify_report', modifyReportCache)

        /** 通知修改报告工作流完成 */
        workflowStatusManager.notifyStatus('modify_report', 'completed')

        onComplete?.()
      },
      onerror(error) {
        /** 通知修改报告工作流错误 */
        workflowStatusManager.notifyStatus('modify_report', 'error', error)

        onError?.(error as Error)
      },
    })
  }
  catch (error) {
    /** 通知修改报告工作流错误 */
    workflowStatusManager.notifyStatus('modify_report', 'error', error)

    onError?.(error as Error)
  }
}
