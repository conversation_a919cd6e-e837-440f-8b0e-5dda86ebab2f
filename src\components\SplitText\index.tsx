import type { CSSProperties } from 'react'
import classnames from 'clsx'
import { memo } from 'react'

function _SplitText({
  style,
  className,
  children,
}: SplitTextProps) {
  return <div
    className={ classnames(
      'flex items-center justify-center w-full',
      className,
    ) }
    style={ style }
  >
    <div className="h-[1px] flex-1 bg-[#9984]"></div>
    <span className="mx-2 font-bold">{children || 'No Data'}</span>
    <div className="h-[1px] flex-1 bg-[#9984]"></div>
  </div>
}

export const SplitText = memo<SplitTextProps>(_SplitText)
SplitText.displayName = 'SplitText'

export type SplitTextProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode
}
