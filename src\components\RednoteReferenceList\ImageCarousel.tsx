import type { EmblaCarouselType } from 'embla-carousel'
import useEmblaCarousel from 'embla-carousel-react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'

interface ImageCarouselProps {
  /** 图片数组 */
  images: string[]
  /** 当前图片索引 */
  currentIndex: number
  /** 索引变化回调 */
  onIndexChange: (index: number) => void
  /** 自定义样式类名 */
  className?: string
  /** 是否显示计数器 */
  showCounter?: boolean
  /** 是否显示指示器 */
  showIndicators?: boolean
  /** 指示器最大显示数量 */
  maxIndicators?: number
}

const ImageCarousel = memo<ImageCarouselProps>(({
  images,
  currentIndex,
  onIndexChange,
  className = '',
  showCounter = true,
  showIndicators = true,
  maxIndicators = 5,
}) => {
  /** 图片加载状态管理 */
  const [imageLoadStates, setImageLoadStates] = useState<Record<string, 'loading' | 'loaded' | 'error'>>({})

  /** 创建占位图的 data URI */
  const placeholderDataUri = useMemo(() => {
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="white"/>
        <g transform="translate(400, 300)">
          <rect x="-60" y="-60" width="120" height="90" fill="none" stroke="#9CA3AF" stroke-width="2" rx="4"/>
          <circle cx="-30" cy="-30" r="12" fill="none" stroke="#9CA3AF" stroke-width="2"/>
          <path d="M -60,30 L -15,-15 L 15,15 L 60,30" fill="none" stroke="#9CA3AF" stroke-width="2"/>
          <text x="0" y="70" font-family="Arial, sans-serif" font-size="18" fill="#9CA3AF" text-anchor="middle">Image unavailable</text>
        </g>
      </svg>
    `)}`
  }, [])

  /** 安全的图片数组处理 */
  const safeImages = useMemo(() => {
    if (!images || !Array.isArray(images) || images.length === 0) {
      return []
    }
    return images.filter(img => img && typeof img === 'string' && img.trim() !== '')
  }, [images])

  const hasMultipleImages = safeImages.length > 1

  /** Embla Carousel 配置 - 禁用循环以实现智能导航按钮 */
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false, // 禁用循环以便正确检测边界
    startIndex: currentIndex,
    skipSnaps: false,
    dragFree: false,
    align: 'center',
  })

  /** 当前选中的索引状态 */
  const [selectedIndex, setSelectedIndex] = useState(currentIndex)

  /** 导航按钮显示状态 */
  const [canScrollPrev, setCanScrollPrev] = useState(false)
  const [canScrollNext, setCanScrollNext] = useState(false)

  /** 图片加载处理 - 静默处理错误，不输出控制台信息 */
  const handleImageLoad = useCallback((src: string) => {
    setImageLoadStates(prev => ({ ...prev, [src]: 'loaded' }))
  }, [])

  const handleImageError = useCallback((src: string) => {
    setImageLoadStates(prev => ({ ...prev, [src]: 'error' }))
  }, [])

  /** 导航处理 */
  const handlePrevious = useCallback(() => {
    if (!hasMultipleImages || !emblaApi)
      return
    emblaApi.scrollPrev()
  }, [hasMultipleImages, emblaApi])

  const handleNext = useCallback(() => {
    if (!hasMultipleImages || !emblaApi)
      return
    emblaApi.scrollNext()
  }, [hasMultipleImages, emblaApi])

  const handleIndicatorClick = useCallback((index: number) => {
    if (!emblaApi || index < 0 || index >= safeImages.length)
      return
    emblaApi.scrollTo(index)
  }, [emblaApi, safeImages.length])

  /** 更新导航按钮状态 */
  const updateNavigationState = useCallback(() => {
    if (!emblaApi) {
      setCanScrollPrev(false)
      setCanScrollNext(false)
      return
    }

    // 如果只有一张图片或没有图片，不显示导航按钮
    if (safeImages.length <= 1) {
      setCanScrollPrev(false)
      setCanScrollNext(false)
      return
    }

    // 特殊情况：如果图片数量较少（≤5张）且所有内容都能在可视区域内显示
    // 这种情况下可能不需要导航按钮
    if (safeImages.length <= 5) {
      // 检查容器是否足够宽以显示所有图片
      const containerElement = emblaApi.containerNode()
      const slideElements = emblaApi.slideNodes()

      if (containerElement && slideElements.length > 0) {
        const containerWidth = containerElement.offsetWidth
        const totalSlidesWidth = slideElements.reduce((total, slide) => total + slide.offsetWidth, 0)

        // 如果所有图片都能在容器内显示，不需要导航按钮
        if (totalSlidesWidth <= containerWidth) {
          setCanScrollPrev(false)
          setCanScrollNext(false)
          return
        }
      }
    }

    // 检查实际的滚动能力
    const canPrev = emblaApi.canScrollPrev()
    const canNext = emblaApi.canScrollNext()

    // 调试日志
    console.log('🔄 [ImageCarousel] 导航状态更新:', {
      imagesCount: safeImages.length,
      canPrev,
      canNext,
      selectedIndex: emblaApi.selectedScrollSnap()
    })

    setCanScrollPrev(canPrev)
    setCanScrollNext(canNext)
  }, [emblaApi, safeImages.length])

  /** 监听 Embla 事件 */
  useEffect(() => {
    if (!emblaApi)
      return

    const onSelect = () => {
      const index = emblaApi.selectedScrollSnap()
      setSelectedIndex(index)
      onIndexChange(index)
      updateNavigationState()
    }

    const onInit = () => {
      updateNavigationState()
    }

    emblaApi.on('select', onSelect)
    emblaApi.on('init', onInit)
    onSelect() // 初始化

    return () => {
      emblaApi.off('select', onSelect)
      emblaApi.off('init', onInit)
    }
  }, [emblaApi, onIndexChange, updateNavigationState])

  /** 外部 currentIndex 变化时同步到 Embla */
  useEffect(() => {
    if (!emblaApi || selectedIndex === currentIndex)
      return
    emblaApi.scrollTo(currentIndex)
  }, [emblaApi, currentIndex, selectedIndex])

  /** 图片数组变化时更新导航状态 */
  useEffect(() => {
    updateNavigationState()
  }, [safeImages.length, updateNavigationState])

  /** 监听窗口大小变化，更新导航状态 */
  useEffect(() => {
    if (!emblaApi) return

    const handleResize = () => {
      // 延迟更新以确保 DOM 已经重新渲染
      setTimeout(() => {
        updateNavigationState()
      }, 100)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [emblaApi, updateNavigationState])

  /** 预加载相邻图片 - 静默处理 */
  useEffect(() => {
    if (safeImages.length === 0)
      return

    const preloadImages = [
      safeImages[selectedIndex],
      safeImages[selectedIndex - 1],
      safeImages[selectedIndex + 1],
    ].filter(Boolean)

    preloadImages.forEach((src) => {
      if (src && !imageLoadStates[src]) {
        setImageLoadStates(prev => ({ ...prev, [src]: 'loading' }))
        const img = new Image()
        img.onload = () => handleImageLoad(src)
        img.onerror = () => handleImageError(src)
        img.src = src
      }
    })
  }, [selectedIndex, safeImages, imageLoadStates, handleImageLoad, handleImageError])

  /** 计算指示器显示范围 */
  const getIndicatorRange = useCallback(() => {
    if (safeImages.length <= maxIndicators) {
      return { start: 0, end: safeImages.length - 1 }
    }

    const half = Math.floor(maxIndicators / 2)
    let start = Math.max(0, selectedIndex - half)
    let end = Math.min(safeImages.length - 1, start + maxIndicators - 1)

    if (end - start + 1 < maxIndicators) {
      start = Math.max(0, end - maxIndicators + 1)
    }

    return { start, end }
  }, [safeImages.length, maxIndicators, selectedIndex])

  const { start, end } = getIndicatorRange()

  /** 获取当前显示的图片源 */
  const getCurrentImageSrc = useCallback((src: string) => {
    const loadState = imageLoadStates[src]

    /** 如果图片加载失败或正在加载，返回 null（用于显示占位图） */
    if (loadState === 'error' || !src) {
      return null
    }

    return src
  }, [imageLoadStates])

  /** 如果没有有效图片，显示占位符 */
  if (safeImages.length === 0) {
    return (
      <div className={ `h-full w-full ${className}` }>
        <img
          src={ placeholderDataUri }
          alt="Image unavailable"
          className="h-full w-full object-cover"
        />
      </div>
    )
  }

  return (
    <div className={ `relative h-full w-full ${className}` }>
      {/* Embla Carousel 容器 */}
      <div className="h-full w-full overflow-hidden" ref={ emblaRef }>
        <div className="h-full flex">
          {safeImages.map((src, index) => {
            const imageSrc = getCurrentImageSrc(src)
            return (
              <div key={ index } className="relative min-w-0 flex-[0_0_100%]">
                <img
                  src={ imageSrc || placeholderDataUri }
                  alt={ `Image ${index + 1} of ${safeImages.length}` }
                  className="h-full w-full object-cover"
                  onLoad={ () => handleImageLoad(src) }
                  onError={ () => handleImageError(src) }
                  style={ {
                    transition: 'opacity 0.3s ease-in-out',
                    opacity: imageLoadStates[src] === 'loading'
                      ? 0.7
                      : 1,
                  } }
                />
              </div>
            )
          })}
        </div>
      </div>

      {/* 图片计数器 */}
      {showCounter && hasMultipleImages && (
        <div className="absolute right-4 top-4 rounded-xl bg-black bg-opacity-60 px-2 py-1 text-xs text-white">
          {selectedIndex + 1}
          /
          {safeImages.length}
        </div>
      )}

      {/* 左右导航按钮 - 基于滚动状态智能显示 */}
      {hasMultipleImages && (
        <>
          {/* 左导航按钮 - 只在可以向左滚动时显示 */}
          {canScrollPrev && (
            <button
              className="absolute left-3 top-1/2 h-8 w-8 flex items-center justify-center rounded-full bg-black bg-opacity-60 text-white transition-all duration-200 -translate-y-1/2 hover:scale-110 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              onClick={ handlePrevious }
              aria-label="Previous image"
              type="button"
            >
              <ChevronLeft size={ 16 } />
            </button>
          )}

          {/* 右导航按钮 - 只在可以向右滚动时显示 */}
          {canScrollNext && (
            <button
              className="absolute right-3 top-1/2 h-8 w-8 flex items-center justify-center rounded-full bg-black bg-opacity-60 text-white transition-all duration-200 -translate-y-1/2 hover:scale-110 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
              onClick={ handleNext }
              aria-label="Next image"
              type="button"
            >
              <ChevronRight size={ 16 } />
            </button>
          )}
        </>
      )}

      {/* 底部指示器 - 只在有多张图片时显示 */}
      {hasMultipleImages && showIndicators && (
        <div className="absolute bottom-4 left-1/2 flex items-center gap-2 -translate-x-1/2">
          {/* 左省略号 */}
          {start > 0 && (
            <div className="flex items-center gap-1">
              <button
                className="h-2 w-2 rounded-full bg-white bg-opacity-50 transition-all duration-200 hover:bg-opacity-70 focus:outline-none focus:ring-1 focus:ring-white focus:ring-opacity-50"
                onClick={ () => handleIndicatorClick(0) }
                aria-label="Go to first image"
                type="button"
              />
              {start > 1 && <span className="text-xs text-white opacity-70">...</span>}
            </div>
          )}

          {/* 主要指示器 */}
          {Array.from({ length: end - start + 1 }, (_, i) => {
            const index = start + i
            const isActive = index === selectedIndex
            return (
              <button
                key={ index }
                className={ `h-2 w-2 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-white focus:ring-opacity-50 ${
                  isActive
                    ? 'bg-white scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-70 hover:scale-110'
                }` }
                onClick={ () => handleIndicatorClick(index) }
                aria-label={ `Go to image ${index + 1}` }
                type="button"
              />
            )
          })}

          {/* 右省略号 */}
          {end < safeImages.length - 1 && (
            <div className="flex items-center gap-1">
              {end < safeImages.length - 2 && <span className="text-xs text-white opacity-70">...</span>}
              <button
                className="h-2 w-2 rounded-full bg-white bg-opacity-50 transition-all duration-200 hover:bg-opacity-70 focus:outline-none focus:ring-1 focus:ring-white focus:ring-opacity-50"
                onClick={ () => handleIndicatorClick(safeImages.length - 1) }
                aria-label="Go to last image"
                type="button"
              />
            </div>
          )}
        </div>
      )}
    </div>
  )
})

ImageCarousel.displayName = 'ImageCarousel'

export default ImageCarousel
