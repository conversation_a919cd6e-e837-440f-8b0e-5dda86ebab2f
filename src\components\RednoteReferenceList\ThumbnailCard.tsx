import classNames from 'clsx'
import { Heart } from 'lucide-react'
import { memo, useCallback, useMemo, useState } from 'react'

interface ThumbnailCardProps {
  post: {
    id: string
    image: string[]
    title: string
    user: { name: string }
    stats: { likes: string }
  }
  isSelected: boolean
  isPreview: boolean
  onPreview: (postId: string) => void
  selectNoteId: string
}

const ThumbnailCard = memo<ThumbnailCardProps>(({
  post,
  isSelected,
  isPreview,
  onPreview,
  selectNoteId,
}) => {
  const [imageError, setImageError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  const handleImageError = useCallback(() => {
    setImageError(true)
  }, [])

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true)
  }, [])

  const handleClick = useCallback(() => {
    onPreview(post.id)
  }, [post.id, onPreview])

  /** 使用 data URI 作为占位图 - 与 ImageCarousel 保持一致 */
  const placeholderDataUri = useMemo(() => {
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="white"/>
        <g transform="translate(150, 100)">
          <rect x="-40" y="-40" width="80" height="60" fill="none" stroke="#9CA3AF" stroke-width="2" rx="4"/>
          <circle cx="-20" cy="-20" r="8" fill="none" stroke="#9CA3AF" stroke-width="2"/>
          <path d="M -40,20 L -10,-10 L 10,10 L 40,20" fill="none" stroke="#9CA3AF" stroke-width="2"/>
          <text x="0" y="50" font-family="Arial, sans-serif" font-size="14" fill="#9CA3AF" text-anchor="middle">Image unavailable</text>
        </g>
      </svg>
    `)}`
  }, [])

  const imageSrc = imageError
    ? placeholderDataUri
    : post.image?.[0] || placeholderDataUri

  return (
    <div
      onClick={ handleClick }
      className={ classNames(
        'relative flex-shrink-0 cursor-pointer overflow-hidden rounded-lg bg-white shadow-md transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
        /** 响应式宽度：移动端显示2个，平板显示3个，桌面显示5个 */
        'w-[calc(50%-8px)] lg:w-[calc(20%-12px)] sm:w-[calc(33.333%-11px)]',
        {
          'border-2 border-blue-100': isPreview,
        },
      ) }
    >
      {post.id === selectNoteId && (
        <div className="absolute right-[12px] top-[12px] z-10 items-center rounded-full bg-gray-100 px-2 py-0.5 text-sm text-gray-700">
          Selected
        </div>
      )}

      <div className="relative h-[200px] w-full overflow-hidden">
        <img
          src={ imageSrc }
          alt="Thumbnail"
          className={ classNames(
            'h-full w-full object-cover transition-opacity duration-300',
            {
              'opacity-0': !imageLoaded && !imageError,
              'opacity-100': imageLoaded || imageError,
            },
          ) }
          onLoad={ handleImageLoad }
          onError={ handleImageError }
          loading="lazy"
        />

        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex animate-pulse items-center justify-center bg-gray-200">
            <div className="h-8 w-8 animate-spin border-2 border-gray-300 border-t-blue-500 rounded-full" />
          </div>
        )}
      </div>

      <div className="p-3">
        <p className="line-clamp-2 mb-2 h-[30px] text-xs text-gray-700 leading-tight">
          {post.title}
        </p>
        <div className="flex items-center gap-1.5">
          <img
            src="/src/assets/image/home/<USER>/1.webp"
            alt="User"
            className="h-4 w-4 rounded-full"
            onError={ (e) => {
              const target = e.currentTarget as HTMLImageElement
              target.style.display = 'none'
            } }
          />
          <span className="flex-1 text-xs text-gray-600">{post.user.name}</span>
          <span className="flex items-center gap-1 text-xs text-gray-400">
            <Heart size={ 12 } />
            {post.stats.likes}
          </span>
        </div>
      </div>
    </div>
  )
})

ThumbnailCard.displayName = 'ThumbnailCard'

export default ThumbnailCard
