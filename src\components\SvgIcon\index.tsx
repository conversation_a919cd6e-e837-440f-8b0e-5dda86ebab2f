import type { ButtonProps } from 'antd'
import type { CSSProperties, FC } from 'react'
import classNames from 'clsx'
import styles from './index.module.css'

interface Props extends Pick<ButtonProps, 'onClick'> {
  icon: string
  className?: string
  style?: CSSProperties
  noFill?: boolean
  disabled?: boolean
}

const allIcons: Record<string, any> = import.meta.glob(
  '/src/assets/svg/*.svg',
  {
    eager: true,
  },
)

const SvgIcon: FC<Props> = ({
  icon,
  className,
  style,
  noFill = false,
  disabled = false,
  ...restProps
}) => {
  const Icon = allIcons[`/src/assets/svg/${icon}.svg`].default || null

  return (
    <Icon
      className={ classNames(
        styles['svg-icon'],
        {
          [styles['no-fill']]: noFill,
          [styles.disabled]: disabled,
        },
        className,
      ) }
      style={ style }
      { ...restProps }
    />
  )
}

export default SvgIcon
