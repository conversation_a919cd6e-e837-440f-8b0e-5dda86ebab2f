import clsx from 'clsx'
import { memo } from 'react'

export const MediaCard = memo<MediaCardProps>(({ children }) => {
  return (
    <div className="aspect-[0] flex flex-col border border-[#fff]/10 rounded-lg bg-[#18181B] p-[2rem]">{children}</div>
  )
})

const mediaList: Media[] = [
  {
    id: 1,
    img: new URL('@/assets/image/media-x.webp', import.meta.url).href,
    myIcon: new URL('@/assets/svg/photoG.svg', import.meta.url).href,
    platform: 'X',
  },
  {
    id: 2,
    img: new URL('@/assets/image/media-linkedin.webp', import.meta.url).href,
    myIcon: new URL('@/assets/svg/photoG.svg', import.meta.url).href,
    platform: 'LinkedIn',
  },
  {
    id: 3,
    img: new URL('@/assets/image/media-facebook.webp', import.meta.url).href,
    myIcon: new URL('@/assets/svg/photoG.svg', import.meta.url).href,
    platform: 'Facebook',
  },
  {
    id: 4,
    img: new URL('@/assets/image/media-pinterest.webp', import.meta.url).href,
    myIcon: new URL('@/assets/svg/photoG.svg', import.meta.url).href,
    platform: 'Pinterest',
  },
]

export function SocialMediaModule() {
  return (
    <div className="grid grid-cols-[repeat(auto-fit,minmax(20vw,1fr))] my-20 h-fit w-full gap-4 px-4 text-[#fff] [@media(max-width:1240px)]:grid-cols-[repeat(auto-fit,minmax(400px,1fr))] [@media(max-width:900px)]:grid-cols-[repeat(auto-fit,minmax(100%,1fr))]">
      {mediaList.map(({ id, img, platform, myIcon }) => (<MediaCard key={ id }>
        <img className="aspect-[0.83] w-full object-cover object-top" src={ img } alt={ platform } />
        <div className={ clsx('mt-auto flex gap-1') }>
          <img src={ myIcon } alt={ `photog-${platform}` } />
          <div className="mt-6">
            <div className="text-[16px] font-700">PhotoG</div>
            <div className="text-[16px] text-[#fff]/60">{platform}</div>
          </div>
        </div>
      </MediaCard>))}
    </div>
  )
}

interface MediaCardProps {
  children?: React.ReactNode
}

interface Media {
  id: number
  myIcon: string
  img?: string
  platform: string
}
