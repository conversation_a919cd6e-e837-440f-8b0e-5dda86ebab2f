import type { StepProps, StepsProps } from './types'
import { useBindWinEvent } from '@/hooks'
import cx from 'clsx'
import { FONT_SIZE_MULTIPLE } from './constants'
import { StepIcon } from './StepIcon'

export const StepItem = memo<StepProps & {
  index: number
  progressDot?: StepsProps['progressDot']
  size?: StepsProps['size']
}>((
  {
    title,
    desc,
    icon,
    status,
    disabled,
    className,
    index,
    progressDot,
    size = 18,
  },
) => {
  const stepSize = { width: size, height: size }
  const fontSize = { fontSize: size * FONT_SIZE_MULTIPLE }

  const el = useRef<HTMLDivElement>(null)
  const loading = status === 'process'
  const [maxSize, setMaxSize] = useState(0)

  useEffect(setLayoutSize, [])
  useBindWinEvent('resize', setLayoutSize)

  function setLayoutSize() {
    const { width, height } = el.current?.getBoundingClientRect() || { width: 0, height: 0 }
    const maxSize = Math.max(width, height)
    setMaxSize(maxSize)
  }

  return (
    <div
      ref={ el }
      className={ cx(
        'flex relative rounded-lg items-center gap-2 overflow-hidden',
        disabled && 'opacity-50 cursor-not-allowed',
        { 'shadow-md border border-gray-200': !loading },
        { 'p-[1px]': loading },
        className,
      ) }
    >
      {/* 流光边框 */ }
      <div
        className={ cx(
          'absolute left-1/2 top-1/2 -z-1',
          { 'animate-spin': true },
        ) }
        style={ {
          backgroundImage: true
            ? 'conic-gradient(#fff000, #00f3ff'
            : '',
          width: maxSize * 2,
          height: maxSize * 2,
          translate: '-50% -50%',
          animationDuration: '1.5s',
          animationTimingFunction: 'linear',
        } }
      ></div>

      <div className={ cx(
        'size-full relative z-1 rounded-md flex items-center gap-2 py-2 px-3 bg-white',
      ) }>
        <div
          className={ cx(
            'flex items-center justify-center rounded-full transition-all duration-300',
            { 'p-[2px]': !icon },
            status === 'finish'
              ? 'bg-primary text-white'
              : status === 'error'
                ? 'bg-red-500 text-white'
                : status === 'process'
                  ? 'bg-blue-100 text-blue-500'
                  : 'text-gray-500',
          ) }
          style={ stepSize }
        >
          <StepIcon
            status={ status }
            index={ index }
            icon={ icon }
            progressDot={ progressDot }
            size={ size }
          />
        </div>

        <div
          className={ cx(
            'flex flex-col',
            { 'opacity-50': status === 'wait' },
          ) }
        >
          <div
            className={ cx(
              { 'text-black': status === 'finish' },
              { 'text-red-500': status === 'error' },
              { 'text-blue-500': status === 'process' },
              { 'text-gray-600': status === 'wait' },
            ) }
            style={ {
              ...fontSize,
            } }
          >
            { title }
          </div>
          <div
            className="text-gray-400"
            style={ {
              lineHeight: 0.9,
              fontSize: size * FONT_SIZE_MULTIPLE * 0.8,
            } }
          >
            { desc }
          </div>
        </div>
      </div>
    </div>
  )
})

StepItem.displayName = 'StepItem'
