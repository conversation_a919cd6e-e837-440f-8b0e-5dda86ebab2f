import type { CSSProperties } from 'react'
import { LAYOUT_SIZE } from '@/components/RootLayout/constants'
import SvgIcon from '@/components/SvgIcon'
import { RechargeTypeEnum } from '@/dts'

import { useNavi, useT } from '@/hooks'
import { logout, userDetailStore } from '@/store/userStore'
import classnames from 'clsx'
import { memo } from 'react'
import { useSnapshot } from 'valtio'

function _LeftAside({
  style,
  className,
}: LeftAsideProps) {
  const t = useT()
  const { pathname } = useLocation()

  const items = useMemo(() => {
    const activeAddon = (p: string) =>
      pathname.startsWith(p)
        ? '-active'
        : ''

    return [
      {
        key: '/p/trend',
        icon: (
          <SvgIcon
            icon={ `menu-history${activeAddon('/p/trend')}` }
            noFill
          />
        ),
        label: t('layout.nav-home'),
      },
      {
        key: '/p/awsChat',
        icon: (
          <SvgIcon
            icon={ `menu-lab${activeAddon('/p/awsChat')}` }
            noFill
          />
        ),
        label: t('layout.nav-lab'),
      },
      {
        key: '/p/history',
        icon: (
          <SvgIcon
            icon={ `menu-square${activeAddon('/p/history')}` }
            noFill
          />
        ),
        label: t('layout.nav-square'),
      },

      {
        key: '/p/photog',
        icon: (
          <SvgIcon
            icon={ `menu-photog${activeAddon('/p/photog')}` }
            noFill
          />
        ),
        label: t('layout.nav-photo'),
      },
      {
        key: '/p/video',
        icon: (
          <SvgIcon
            icon={ `menu-video${activeAddon('/p/video')}` }
            noFill
          />
        ),
        label: t('layout.nav-video'),
      },

      {
        key: '/p/models',
        icon: (
          <SvgIcon
            icon={ `menu-assets${activeAddon('/p/models')}` }
            noFill
          />
        ),
        label: t('layout.nav-items'),
      },
      {
        key: '/p/assets',
        icon: (
          <SvgIcon
            icon={ `menu-asset${activeAddon('/p/assets')}` }
            noFill
          />
        ),
        label: t('layout.nav-assets'),
      },
    ]
  }, [t, pathname])

  const userinfo = useSnapshot(userDetailStore)
  const to = useNavi()

  return <div
    className={ classnames(
      'bg-white',
      className,
    ) }
    style={ {
      ...style,
      width: LAYOUT_SIZE.leftAsideWidth,
    } }
  >
    <div className="h-full flex flex-col gap-4 p-2">

      <div
        className={ classnames(
          `flex flex-col cursor-pointer items-center gap-3 text-[12px]`,
          'py-2 transition hover:bg-innerBg',
        ) }
        onClick={ () => to('/pricing', { state: RechargeTypeEnum.MONTHLY }) }
      >
        <SvgIcon icon="charge" noFill className="h-6 w-6"></SvgIcon>
        <span>{userinfo.totalCredits}</span>
      </div>
      <div
        className={ classnames(
          `flex flex-col cursor-pointer items-center gap-3 text-[12px]`,
          'py-2 transition hover:bg-innerBg',
        ) }
        onClick={ () => to('/pricing') }
      >
        <SvgIcon icon="credit3" noFill className="h-6 w-6"></SvgIcon>
        <span>Pro</span>
      </div>

      {/* 分割线 */}
      <div className="h-[1px] w-full bg-innerBg"></div>

      {/* 菜单 */}
      {items.map(item => <div
        key={ item.key }
        className={ classnames(
          `flex flex-col cursor-pointer items-center gap-1 text-[12px] font-bold`,
          pathname !== item.key && 'font-normal opacity-50',
          'py-2 transition hover:bg-innerBg',
        ) }
        onClick={ () => {
          to(item.key as any)
        } }
        data-id={ item.key }
      >
        {item.icon}
        <span>{item.label}</span>
      </div>,
      )}

      {/* 退出登录 */}
      <div
        onClick={ () => logout().then(() => {
          to('/')
        }) }
        className="mt-auto flex flex-col cursor-pointer items-center gap-1 py-2 text-[12px] text-red-500 transition hover:bg-innerBg"
      >
        <SvgIcon icon="logout" noFill />
        <div>Sign out</div>
      </div>
    </div>
  </div>
}

export const LeftAside = memo<LeftAsideProps>(_LeftAside)
LeftAside.displayName = 'LeftAside'

export type LeftAsideProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode
}
