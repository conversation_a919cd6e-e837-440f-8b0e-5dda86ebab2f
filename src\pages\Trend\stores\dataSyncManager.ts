/**
 * 数据同步管理器
 * 
 * 功能：
 * - 统一管理组件间的数据同步状态
 * - 确保数据准备完成后再通知UI组件
 * - 提供可靠的回调通知机制
 * - 处理异步数据加载的时序问题
 */

import type { StateStoreType } from './index'

export class DataSyncManager {
  private stateStore: StateStoreType
  private callbacks: Map<string, Array<() => void>> = new Map()
  private dataReadyFlags: Map<string, boolean> = new Map()

  constructor(stateStore: StateStoreType) {
    this.stateStore = stateStore
  }

  /**
   * 注册数据准备完成的回调
   */
  onDataReady(dataType: string, callback: () => void): () => void {
    if (!this.callbacks.has(dataType)) {
      this.callbacks.set(dataType, [])
    }
    
    const callbacks = this.callbacks.get(dataType)!
    callbacks.push(callback)

    // 如果数据已经准备好，立即执行回调
    if (this.dataReadyFlags.get(dataType)) {
      setTimeout(callback, 0)
    }

    // 返回取消订阅函数
    return () => {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 标记数据准备完成
   */
  markDataReady(dataType: string, data?: any): void {
    console.warn(`[DataSyncManager] 标记数据准备完成: ${dataType}`, data)
    
    this.dataReadyFlags.set(dataType, true)
    
    // 更新状态存储
    this.updateStateStore(dataType, true)
    
    // 执行所有回调
    const callbacks = this.callbacks.get(dataType) || []
    callbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error(`[DataSyncManager] 回调执行错误 (${dataType}):`, error)
      }
    })

    // 检查是否所有数据都准备完成
    this.checkAllDataReady()
  }

  /**
   * 检查数据是否准备完成
   */
  isDataReady(dataType: string): boolean {
    return this.dataReadyFlags.get(dataType) || false
  }

  /**
   * 重置数据状态
   */
  resetDataStatus(dataType?: string): void {
    if (dataType) {
      this.dataReadyFlags.set(dataType, false)
      this.updateStateStore(dataType, false)
    } else {
      this.dataReadyFlags.clear()
      this.resetAllStateStore()
    }
  }

  /**
   * 更新状态存储
   */
  private updateStateStore(dataType: string, isReady: boolean): void {
    switch (dataType) {
      case 'distillDarenList':
        this.stateStore.dataPreparationStatus.distillDarenListDataReady = isReady
        break
      case 'hotpotsAnalysis':
        this.stateStore.dataPreparationStatus.hotpotsAnalysisDataReady = isReady
        break
      case 'operationsThinking':
        this.stateStore.dataPreparationStatus.operationsThinkingCompleted = isReady
        this.stateStore.operationsThinking1Completed = isReady
        break
    }
  }

  /**
   * 检查所有数据是否准备完成
   */
  private checkAllDataReady(): void {
    const { dataPreparationStatus } = this.stateStore
    const allReady = dataPreparationStatus.distillDarenListDataReady && 
                     dataPreparationStatus.operationsThinkingCompleted

    if (allReady && !dataPreparationStatus.allDataReady) {
      dataPreparationStatus.allDataReady = true
      console.warn('[DataSyncManager] 所有数据准备完成')
      
      // 执行全局完成回调
      this.markDataReady('allDataReady')
    }
  }

  /**
   * 重置所有状态存储
   */
  private resetAllStateStore(): void {
    const status = this.stateStore.dataPreparationStatus
    status.distillDarenListDataReady = false
    status.hotpotsAnalysisDataReady = false
    status.operationsThinkingCompleted = false
    status.allDataReady = false
    this.stateStore.operationsThinking1Completed = false
  }

  /**
   * 获取当前数据状态摘要
   */
  getDataStatusSummary(): Record<string, boolean> {
    return {
      distillDarenList: this.isDataReady('distillDarenList'),
      hotpotsAnalysis: this.isDataReady('hotpotsAnalysis'),
      operationsThinking: this.isDataReady('operationsThinking'),
      allDataReady: this.isDataReady('allDataReady'),
    }
  }
}

/**
 * 创建全局数据同步管理器实例
 */
export function createDataSyncManager(stateStore: StateStoreType): DataSyncManager {
  return new DataSyncManager(stateStore)
}
