import { cn } from '@/utils'
import { motion } from 'framer-motion'
import { ChevronDown } from 'lucide-react'
import { isValidElement, memo, useMemo, useState } from 'react'
import { AnimateShow } from '../Animate'

export const Dropdown = memo<DropdownProps>(({
  items,
  selectedId,
  onClick,
  accordion = true,

  className,
  itemClassName,
  sectionHeaderClassName,
  itemTitleClassName,
  itemDescClassName,
  itemActiveClassName,
  itemInactiveClassName,

  defaultExpanded = [],
  renderItem,
}) => {
  const normalizedSections: DropdownSection[] = Array.isArray(items)
    ? items
    : Object.entries(items).map(([name, items]) => ({ name, items }))

  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {}
    normalizedSections.forEach((section) => {
      initial[section.name] = defaultExpanded.includes(section.name)
    })
    return initial
  })

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => {
      if (accordion) {
        const newState: Record<string, boolean> = {}
        normalizedSections.forEach((s) => {
          newState[s.name] = s.name === section
            ? !prev[section]
            : false
        })
        return newState
      }

      return {
        ...prev,
        [section]: !prev[section],
      }
    })
  }

  const defaultRenderItem = useMemo(() => (item: DropdownItem) => (
    <div className="flex items-center gap-3">
      <div className="min-w-0 flex-1">
        <div className="flex items-center justify-between">
          { item.label && (
            <h3 className={ cn('truncate text-sm font-medium text-gray-900 dark:text-gray-100', itemTitleClassName) }>
              { item.label }
            </h3>
          ) }
          { item.timestamp && (
            <span className="text-xs text-gray-400 dark:text-gray-500">
              { new Date(item.timestamp).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              }) }
            </span>
          ) }
        </div>

        <div className="mt-1 flex items-center gap-2">
          { item.tag && (
            <span
              className={ cn(
                'rounded-full px-2 py-0.5 text-xs font-medium',
                item.tagColor || 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300',
              ) }
            >
              { item.tag }
            </span>
          ) }
          { item.desc && (
            <p className={ cn('truncate text-sm text-gray-500 dark:text-gray-400', itemDescClassName) }>
              { item.desc }
            </p>
          ) }
        </div>
      </div>
    </div>
  ), [itemDescClassName, itemTitleClassName])

  return (
    <div className={ cn('overflow-y-auto h-full transition-all duration-300', className) }>
      { normalizedSections.map(item => (
        <div
          key={ item.name }
          className={ cn('hover:bg-[#fff]/10', itemClassName) }
        >
          { item.header
            ? (
                <div onClick={ () => toggleSection(item.name) }>
                  { typeof item.header === 'function'
                    ? item.header(expandedSections[item.name])
                    : item.header }
                </div>
              )
            : (
                <div
                  onClick={ () => toggleSection(item.name) }
                  className={ cn(
                    'w-full flex cursor-pointer items-center justify-between px-4 pt-4 pb-1 text-sm text-gray-600 transition-all duration-300 hover:opacity-100 opacity-80 dark:text-gray-300',
                  ) }
                >
                  <span className={ sectionHeaderClassName }>{ item.name }</span>
                  <motion.div
                    animate={ {
                      rotate: expandedSections[item.name]
                        ? 180
                        : 0,
                    } }
                    transition={ { duration: 0.2 } }
                  >
                    <ChevronDown className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                  </motion.div>
                </div>
              ) }

          <AnimateShow
            show={ expandedSections[item.name] }
            className="overflow-hidden"
            visibilityMode
          >
            { isValidElement(item.items)
              ? item.items
              : Array.isArray(item.items) && item.items.length > 0

                ? item.items.map(item => (
                    <motion.div
                      key={ item.id }
                      initial={ { x: -20, opacity: 0 } }
                      animate={ { x: 0, opacity: 1 } }
                      exit={ { x: -20, opacity: 0 } }
                      transition={ { duration: 0.2 } }
                      className={ cn(
                        'py-3 cursor-pointer border-l-4 transition-all duration-300 leading-[1.75] text-[16px] text-[#fff]/60',
                        selectedId === item.id
                          ? ['bg-blue-50 border-blue-500 dark:bg-blue-500/15 dark:border-blue-500/50', itemActiveClassName]
                          : ['border-transparent hover:bg-slate-100 hover:border-slate-300 dark:hover:bg-slate-700/50 dark:hover:border-slate-600', itemInactiveClassName],
                      ) }
                      onClick={ () => onClick?.(item.id) }
                    >
                      { item.customContent || (renderItem
                        ? renderItem(item)
                        : defaultRenderItem(item)) }
                    </motion.div>
                  ))
                : null }
          </AnimateShow>
        </div>
      )) }
    </div>
  )
})

export interface DropdownItem {
  id: string
  label?: string
  desc?: string
  timestamp?: Date | string | number
  tag?: string
  tagColor?: string
  customContent?: React.ReactNode
}

export interface DropdownSection {
  name: string
  items: DropdownItem[] | React.ReactNode
  header?: React.ReactNode | ((isExpanded: boolean) => React.ReactNode)
}

export interface DropdownProps {
  items:
    | Record<string, DropdownItem[] | React.ReactNode>
    | DropdownSection[]

  className?: string
  itemClassName?: string
  sectionHeaderClassName?: string
  itemTitleClassName?: string
  itemDescClassName?: string
  itemActiveClassName?: string
  itemInactiveClassName?: string

  selectedId?: string | null
  onClick?: (id: string) => void
  accordion?: boolean

  defaultExpanded?: string[]
  renderItem?: (item: DropdownItem) => React.ReactNode
}
