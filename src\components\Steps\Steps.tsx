import type { StepsProps } from './types'
import cx from 'clsx'
import { ChevronUp, CircleCheck, CircleDashed, Loader2 } from 'lucide-react'
import { StepItem } from './StepItem'

export const Steps = memo((
  {
    expandDirection = 'down',
    progressDot = false,
    size = 18,
    items,
    className,
    slotClassName,
    showProgress = true,
    expandable = true,
    children,
  }: StepsProps,
) => {
  const [expanded, setExpanded] = useState(false)

  // Calculate the current step based on the items' status
  const current = useMemo(() => {
    // Find the index of the last item with status "finish"
    const lastFinishedIndex = items.reduce((lastIndex, item, index) => {
      return item.status === 'finish'
        ? index
        : lastIndex
    }, -1)

    // Current is the next step after the last finished one
    return lastFinishedIndex + 1
  }, [items])

  const toggleExpand = useCallback(() => {
    setExpanded(prev => !prev)
  }, [])

  // Generate default content if none provided
  const defaultContent = useMemo(() => {
    const steps = items.map((item, index) => ({
      id: index,
      title: item.title || `Step ${index + 1}`,
      completed: item.status === 'finish',
      inProgress: item.status === 'process',
      icon: item.icon,
    }))

    return (
      <div className={ cx(
        'overflow-auto space-y-3 p-2 min-w-72',
        slotClassName,
      ) }>
        <h3 className="flex flex-col gap-1 text-sm text-gray-900 font-medium">
          <span className="font-bold">Task lists:</span>
          {/* <span className="text-gray-400">
            { `${time}s` }
          </span> */}
        </h3>
        <ul className="space-y-2">
          { steps.map(task => (
            <li key={ task.id } className="flex items-center gap-2">
              { task.completed
                ? <>{ task.icon || <CircleCheck className="h-5 w-5 text-blue-500" /> }</>
                : task.inProgress
                  ? <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                  : <CircleDashed className="h-5 w-5 text-gray-400" /> }
              <span
                className={ cx(
                  'text-sm',
                  task.completed
                    ? 'text-gray-400'
                    : task.inProgress
                      ? 'text-gray-800'
                      : 'text-gray-500',
                ) }
              >
                { task.title }
              </span>

              { task.inProgress && (
                <span className="ml-auto rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-500">
                  In Progress
                </span>
              ) }
            </li>
          )) }
        </ul>
      </div>
    )
  }, [items, slotClassName])

  // Steps and progress section
  const stepsSection = (
    <div className={ cx('flex relative justify-around gap-2') }>
      { items.map((item, index) => <StepItem
        key={ index }
        { ...item }
        index={ index }
        progressDot={ progressDot }
        size={ size }
        className={ cx(
          item.className,
        ) }
      />) }

      { showProgress && items.length > 0 && (
        <div className="flex items-center">
          <span className="text-sm text-gray-500">
            { Math.min(current + 1, items.length) }
            /
            { items.length }
          </span>

          { expandable && (
            <button
              onClick={ toggleExpand }
              className="rounded-md p-1 transition-colors hover:bg-gray-100"
              aria-expanded={ expanded }
              aria-label={ expanded
                ? 'Collapse details'
                : 'Expand details' }
            >
              <div className="transform transition-transform duration-300">
                <ChevronUp
                  className="h-4 w-4 transition-all duration-300"
                  style={ {
                    transform: expanded
                      ? expandDirection === 'down'
                        ? 'rotate(180deg)'
                        : 'rotate(0deg)'
                      : expandDirection === 'down'
                        ? 'rotate(0deg)'
                        : 'rotate(180deg)',
                  } }
                />
              </div>
            </button>
          ) }
        </div>
      ) }
    </div>
  )

  return <div
    className={ cx('rounded-lg border border-gray-200 p-2 relative', className) }
  >
    {/* Main content */ }
    { stepsSection }

    {/* Expandable content with absolute positioning when expanding upward */ }
    { expandable && (
      <div
        className={ cx(
          'transition-all duration-500 ease-in-out',
          expanded
            ? 'opacity-100'
            : 'opacity-0 pointer-events-none',

          expandDirection === 'up'
            ? 'absolute left-1/2 bottom-full mb-2 z-50 bg-white border border-gray-200 rounded-lg p-2 shadow-md -translate-x-1/2'
            : 'border-t border-gray-200',

          expanded && expandDirection === 'up'
            ? 'translate-y-0'
            : 'translate-y-2',

          !expanded && expandDirection === 'down' && 'max-h-0 overflow-hidden',
          expanded && expandDirection === 'down' && 'max-h-96 overflow-auto',
        ) }
      >
        { children || defaultContent }
      </div>
    ) }
  </div>
})

Steps.displayName = 'Steps'
