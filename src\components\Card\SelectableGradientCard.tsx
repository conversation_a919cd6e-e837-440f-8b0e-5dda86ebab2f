import type { ReactNode } from 'react'
import type { ButtonVariant } from '@/components/Button'
import { memo, useEffect, useState } from 'react'
import { Button } from '@/components/Button'
import { cn } from '@/utils'

/**
 * 图标配置接口
 */
export interface IconConfig {
  /** 是否显示图标 */
  show: boolean
  /** 图标元素 */
  icon?: ReactNode
  /** 图标样式类名 */
  className?: string
  /** 图标大小 */
  size?: 'sm' | 'md' | 'lg'
}

/**
 * 按钮配置接口
 */
export interface ButtonConfig {
  /** 按钮文本 */
  text: string
  /** 按钮变体 */
  variant?: ButtonVariant
  /** 按钮样式类名 */
  className?: string
  /** 点击事件 */
  onClick?: () => void
  /** 是否禁用 */
  disabled?: boolean
  /** 按钮位置 */
  position?: 'bottom' | 'bottom-left'
}

/**
 * 内容配置接口
 */
export interface ContentConfig {
  /** 标题 */
  title?: string
  /** 描述 */
  description?: string
  /** 自定义内容 */
  customContent?: ReactNode
  /** 布局方向 */
  layout?: 'vertical' | 'horizontal'
}

/**
 * 社交帖子数据接口
 */
export interface SocialPostData {
  /** 帖子图片 */
  image?: string
  /** 帖子描述 */
  description?: string
  /** 作者信息 */
  author?: {
    name: string
    avatar: string
  }
  /** 统计数据 */
  stats?: {
    likes?: number | string
    shares?: number | string
    views?: number | string
    comments?: number | string
    reads?: number | string
  }
}

/**
 * 卡片配置接口
 */
export interface CardConfig {
  /** 左侧图标配置 */
  leftIcon?: IconConfig
  /** 右侧图标配置 */
  rightIcon?: IconConfig
  /** 内容配置 */
  content?: ContentConfig
  /** 按钮配置 */
  button?: ButtonConfig
  /** 社交帖子数据 */
  socialPost?: SocialPostData
  /** 布局类型 */
  layout?: 'simple' | 'social' | 'report'
}

/**
 * SelectableGradientCard 组件的 Props 类型
 */
export type SelectableGradientCardProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  children?: React.ReactNode
  borderWidth?: number
  borderGradient?: string
  hoverBackground?: string
  selectedBackground?: string
  selected?: boolean
  onSelectedChange?: (selected: boolean) => void
  /** 自定义内容渲染函数 */
  renderContent?: (props: {
    isHovered: boolean
    isSelected: boolean
    contentBackground: string
    onMouseEnter: () => void
    onMouseLeave: () => void
  }) => ReactNode
  /** 内容容器的样式类名 */
  contentClassName?: string
  /** 卡片配置 - 新增的配置选项 */
  cardConfig?: CardConfig
}

/**
 * 可选中的渐变边框卡片组件
 * 特性：
 * 1. 渐变边框始终显示
 * 2. 悬浮时显示背景
 * 3. 支持单选模式（通过父组件管理）
 * 4. 内容区域支持悬浮和选中背景效果
 * 5. 支持多种布局和配置选项
 */
export const SelectableGradientCard = memo((props: SelectableGradientCardProps) => {
  const {
    className,
    children,
    borderWidth = 1.5,
    borderGradient = 'linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
    hoverBackground = 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)',
    selectedBackground = 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)',
    selected = false,
    onSelectedChange,
    renderContent,
    contentClassName,
    cardConfig,
    ...rest
  } = props

  const [isHovered, setIsHovered] = useState(false)
  const [isSelected, setIsSelected] = useState(selected)

  useEffect(() => {
    setIsSelected(selected)
  }, [selected])

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const newSelected = !isSelected
    setIsSelected(newSelected)
    onSelectedChange?.(newSelected)
    rest.onClick?.(e)
  }

  /** 内容背景样式 */
  const contentBackground = (isHovered || isSelected)
    ? (isSelected
        ? selectedBackground
        : hoverBackground)
    : 'transparent'

  /** 渲染图标的辅助函数 */
  const renderIcon = (iconConfig?: IconConfig) => {
    if (!iconConfig?.show || !iconConfig.icon)
      return null

    return (
      <div className={ cn(
        'flex items-center justify-center flex-shrink-0',
        iconConfig.className,
      ) }>
        {iconConfig.icon}
      </div>
    )
  }

  /** 渲染社交帖子的辅助函数 */
  const renderSocialPost = (socialPost?: SocialPostData) => {
    if (!socialPost)
      return null

    return (
      <div className="mt-2 w-37.5 overflow-hidden border border-gray-200 rounded-lg bg-white">
        {socialPost.image && (
          <img
            src={ socialPost.image }
            alt="Social post"
            className="h-48 w-full object-cover"
          />
        )}
        <div className="p-1">
          {socialPost.description && (
            <p className="line-clamp-2 mb-2 text-xs text-gray-600">{socialPost.description}</p>
          )}
          {socialPost.author && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <img
                  src={ socialPost.author.avatar }
                  alt={ socialPost.author.name }
                  className="h-6 w-6 rounded-full"
                />
                <span className="max-w-20 truncate text-xs text-gray-700" style={ { whiteSpace: 'nowrap', textOverflow: 'ellipsis' } }>
                  {socialPost.author.name}
                </span>
              </div>
              {(socialPost.stats?.likes || socialPost.stats?.comments || socialPost.stats?.reads) && (
                <div className="flex items-center text-xs text-gray-500 space-x-2">
                  {socialPost.stats.likes && (
                    <div className="flex items-center space-x-1">
                      <span><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg></span>
                      <span>{socialPost.stats.likes}</span>
                    </div>
                  )}
                  {false && (
                    <div className="flex items-center space-x-1">
                      <span><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg></span>
                      <span>{socialPost.stats.comments}</span>
                    </div>
                  )}
                  {false && (
                    <div className="flex items-center space-x-1">
                      <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                      </span>
                      <span>{socialPost.stats.reads}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  /** 如果提供了 cardConfig，使用新的布局渲染 */
  if (cardConfig) {
    return (
      <div
        className={ cn(
          'relative overflow-hidden rounded-2xl transition-all duration-300',
          className,
        ) }
        style={ {
          border: `${borderWidth}px solid transparent`,
          backgroundClip: 'padding-box, border-box',
          backgroundOrigin: 'padding-box, border-box',
          backgroundImage: `linear-gradient(to right, #fff, #fff), ${borderGradient}`,
        } }
        onClick={ handleClick }
        { ...rest }
      >
        <div
          className={ cn('transition-all duration-300 flex items-start justify-between p-4', contentClassName) }
          style={ { background: contentBackground } }
          onMouseEnter={ () => setIsHovered(true) }
          onMouseLeave={ () => setIsHovered(false) }
        >
          {/* 左侧图标 */}
          {cardConfig.leftIcon && (
            <div className="mr-3 flex-shrink-0">
              {renderIcon(cardConfig.leftIcon)}
            </div>
          )}

          {/* 中间内容区域 */}
          <div className="min-w-0 flex-1">
            {/* 标题和描述 */}
            {(cardConfig.content?.title || cardConfig.content?.description) && (
              <div>
                {cardConfig.content.title && (
                  <h3 className="mb-1 text-gray-900 font-bold">
                    {cardConfig.content.title}
                  </h3>
                )}
                {cardConfig.content.description && (
                  <p className="text-sm text-gray-600">
                    {cardConfig.content.description}
                  </p>
                )}
              </div>
            )}

            {/* 自定义内容 */}
            {cardConfig.content?.customContent && (
              <div className="mb-3">
                {cardConfig.content.customContent}
              </div>
            )}

            {/* 社交帖子 */}
            {cardConfig.socialPost && renderSocialPost(cardConfig.socialPost)}

            {/* 按钮 */}
            {cardConfig.button && (
              <div className={ cn(
                'mt-4',
                cardConfig.button.position === 'bottom-left'
                  ? 'flex justify-start'
                  : 'flex justify-center',
              ) }>
                {cardConfig.button.variant === 'gradient-border' ? (
                  /** 渐变边框按钮的特殊处理 */
                  <div
                    className="relative rounded-full p-[2px]"
                    style={ {
                      background: borderGradient,
                    } }
                  >
                    <Button
                      variant="gradient-border"
                      onClick={ cardConfig.button.onClick }
                      disabled={ cardConfig.button.disabled }
                      className={ cn(
                        'rounded-full px-6 py-2 bg-white hover:bg-gray-50 active:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 dark:active:bg-gray-600',
                        cardConfig.button.className,
                      ) }
                    >
                      {cardConfig.button.text}
                    </Button>
                  </div>
                ) : (
                  /** 普通按钮 */
                  <Button
                    variant={ cardConfig.button.variant || 'primary' }
                    onClick={ cardConfig.button.onClick }
                    disabled={ cardConfig.button.disabled }
                    className={ cn(
                      'rounded-full px-6 py-2',
                      cardConfig.button.className,
                    ) }
                  >
                    {cardConfig.button.text}
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* 右侧图标 */}
          {cardConfig.rightIcon && (
            <div className="ml-3 flex-shrink-0 self-start">
              {renderIcon(cardConfig.rightIcon)}
            </div>
          )}
        </div>
      </div>
    )
  }

  /** 原有的渲染逻辑（保持向后兼容） */
  return (
    <div
      className={ cn(
        'relative overflow-hidden rounded-2xl transition-all duration-300',
        className,
      ) }
      style={ {
        border: `${borderWidth}px solid transparent`,
        backgroundClip: 'padding-box, border-box',
        backgroundOrigin: 'padding-box, border-box',
        backgroundImage: `linear-gradient(to right, #fff, #fff), ${borderGradient}`,
      } }
      onClick={ handleClick }
      { ...rest }
    >
      {/* 如果提供了 renderContent，使用自定义渲染 */}
      {renderContent
        ? (
            renderContent({
              isHovered,
              isSelected,
              contentBackground,
              onMouseEnter: () => setIsHovered(true),
              onMouseLeave: () => setIsHovered(false),
            })
          )
        : (
          /* 默认内容包装 */
            <div
              className={ cn('transition-all duration-300', contentClassName) }
              style={ { background: contentBackground } }
              onMouseEnter={ () => setIsHovered(true) }
              onMouseLeave={ () => setIsHovered(false) }
            >
              {children}
            </div>
          )}
    </div>
  )
})

SelectableGradientCard.displayName = 'SelectableGradientCard'
