import type { CheckboxChangeEvent } from 'antd/es/checkbox'
import { Checkbox } from 'antd'
import clsx from 'clsx'
import { motion } from 'framer-motion'
import { X } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { useSnapshot } from 'valtio'
import RednoteReferenceList from '@/components/RednoteReferenceList'
import { userStore } from '@/store/userStore'
import { cn } from '@/utils'
import { fetchEventSourceWithTimeout } from '@/utils/streamTimeout'
import './TrendSelectionPage.module.css'

/** TrendSelectionPage 骨架屏组件 */
function TrendSelectionSkeleton() {
  return (
    <div className="relative mx-auto max-w-7xl w-full p-6 space-y-8">
      <style>
        {`
        @keyframes shimmer {
          0% { background-position: -200px 0; }
          100% { background-position: calc(200px + 100%) 0; }
        }
        .skeleton-shimmer {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200px 100%;
          animation: shimmer 1.5s infinite;
        }
        .fade-in {
          animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `}
      </style>

      {/* Header Skeleton */}
      <div className="space-y-2">
        <div className="skeleton-shimmer h-8 w-96 rounded"></div>
        <div className="skeleton-shimmer h-4 w-80 rounded"></div>
      </div>

      {/* Main Content Skeleton */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Left Side - Trending Topics Skeleton */}
        <div className="space-y-4">
          <div className="space-y-3">
            {[...new Array(5)].map((_, i) => (
              <div key={ `topic-skeleton-${i}` } className="skeleton-shimmer h-[74px] rounded-[8px]"></div>
            ))}
          </div>
        </div>

        {/* Right Side - Trend Detail Skeleton */}
        <div className="space-y-4">
          <div className="h-[410px] border-[1.5px] border-[#D9D9D9] rounded-2xl p-[16px] space-y-4">
            {/* Content lines skeleton */}
            <div className="space-y-3">
              {[...new Array(8)].map((_, i) => (
                <div
                  key={ `detail-line-${i}` }
                  className={ `skeleton-shimmer h-4 rounded ${
                    i === 7
                      ? 'w-3/4'
                      : 'w-full'
                  }` }></div>
              ))}
            </div>

            {/* Checkbox skeleton */}
            <div className="absolute bottom-4 right-4 flex items-center gap-3">
              <div className="skeleton-shimmer h-4 w-4 rounded"></div>
              <div className="skeleton-shimmer h-4 w-32 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      {/* RednoteReferenceList Skeleton Placeholder */}
      <div className="mt-8">
        <div className="rounded-lg bg-white p-6">
          <div className="skeleton-shimmer mb-2 h-8 w-80 rounded"></div>
          <div className="skeleton-shimmer h-4 w-full rounded"></div>
          <div className="skeleton-shimmer mt-1 h-4 w-3/4 rounded"></div>

          {/* Category Filter Skeleton */}
          <div className="flex items-center py-4">
            <div className="skeleton-shimmer mr-4 h-4 w-32 rounded"></div>
            <div className="skeleton-shimmer h-8 w-24 rounded-full"></div>
          </div>

          {/* Main Content Card Skeleton */}
          <div className="mb-2 h-134 flex overflow-hidden border-2 border-gray-100 rounded-xl">
            {/* Image Section Skeleton */}
            <div className="relative flex-1">
              <div className="skeleton-shimmer absolute inset-0"></div>
            </div>

            {/* Content Section Skeleton */}
            <div className="flex flex-1 flex-col p-6">
              <div className="mb-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="skeleton-shimmer mr-2 h-8 w-8 rounded-full"></div>
                  <div className="skeleton-shimmer mr-2 h-4 w-20 rounded"></div>
                </div>
                <div className="skeleton-shimmer h-6 w-12 rounded-xl"></div>
              </div>

              <div className="mb-4 flex-1">
                <div className="skeleton-shimmer mb-3 h-5 w-full rounded"></div>
                <div className="skeleton-shimmer mb-2 h-5 w-4/5 rounded"></div>
                <div className="skeleton-shimmer mb-3 h-4 w-full rounded"></div>
                <div className="skeleton-shimmer mb-2 h-4 w-full rounded"></div>
                <div className="skeleton-shimmer h-4 w-3/4 rounded"></div>
              </div>

              <div className="mb-4 flex items-center justify-between border-t border-gray-200 pt-4">
                <div className="flex items-center gap-4">
                  {[...new Array(3)].map((_, i) => (
                    <div key={ `stat-${i}` } className="flex items-center gap-1">
                      <div className="skeleton-shimmer h-4 w-4 rounded"></div>
                      <div className="skeleton-shimmer h-4 w-8 rounded"></div>
                    </div>
                  ))}
                </div>
                <div className="skeleton-shimmer h-4 w-20 rounded"></div>
              </div>

              <div className="flex items-center justify-end gap-2">
                <div className="skeleton-shimmer h-4 w-4 rounded"></div>
                <div className="skeleton-shimmer h-4 w-32 rounded"></div>
              </div>
            </div>
          </div>

          {/* Thumbnail Carousel Skeleton */}
          <div className="relative flex items-center gap-3">
            <div className="skeleton-shimmer h-8 w-8 rounded-full"></div>
            <div className="flex flex-1 gap-4 py-2">
              {[...new Array(5)].map((_, i) => (
                <div key={ `thumbnail-${i}` } className="skeleton-shimmer h-24 w-20 flex-shrink-0 rounded-lg"></div>
              ))}
            </div>
            <div className="skeleton-shimmer h-8 w-8 rounded-full"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface TrendTopic {
  id: string
  title: string
  isSelected?: boolean
}

interface TrendDetail {
  id: string
  content: string
  isReference: boolean
}

interface TrendSelectionPageProps {
  planning_report: string
  taskInstanceId?: string
  className: string
  handlerSelectTopic: (topicsId: string) => void
  storeInstance?: any // 传入 store 实例以存储数据
  onClose?: () => void // 添加关闭回调
}

export const TrendSelectionPage: React.FC<TrendSelectionPageProps> = ({
  className,
  handlerSelectTopic = () => { },
  taskInstanceId = '',
  planning_report = '',
  storeInstance,
  onClose,
}) => {
  /** 🎯 添加 loading 状态管理 - 与 OperationsThinkingStream1 同步 */
  const [isLoading, setIsLoading] = useState(true)
  const [operationsThinkingCompleted, setOperationsThinkingCompleted] = useState(false)

  /** 🎯 使用 useSnapshot 确保响应式更新 */
  const stateSnapshot = useSnapshot(storeInstance?.stateStore || {})
  console.log('🚀 ~ TrendSelectionPage ~ stateSnapshot:', stateSnapshot)

  const token = userStore.token

  /** 初始化时检查是否已有数据 */
  const initTopics = () => {
    const existingTopics: TrendTopic[] = []
    const existingDetails: Omit<TrendDetail, 'isReference'>[] = []

    if (storeInstance?.stateStore?.hotpotsTopics) {
      const hotpotsTopics = storeInstance.stateStore.hotpotsTopics
      for (let i = 1; i <= 5; i++) {
        const topicKey = `topic${i}`
        const detailKey = `topic${i}_detail`

        if (hotpotsTopics[topicKey]) {
          existingTopics.push({
            id: topicKey,
            title: hotpotsTopics[topicKey],
          })

          existingDetails.push({
            id: topicKey,
            content: hotpotsTopics[detailKey] || 'Loading details...',
          })
        }
      }
    }

    return { existingTopics, existingDetails }
  }

  const { existingTopics, existingDetails } = initTopics()

  const [topics, setTopics] = useState<TrendTopic[]>(existingTopics)
  const [topicsDetails, setTopicsDetails] = useState<Omit<TrendDetail, 'isReference'>[]>(existingDetails)
  const [preview, setPreview] = useState<TrendDetail | undefined>(
    existingDetails.length > 0
      ? { ...existingDetails[0], isReference: false }
      : undefined,
  )
  /** 使用 store 中的状态，如果存在的话 */
  const [previewTopicId, setPreviewTopicId] = useState<string>(
    storeInstance?.stateStore?.trendSelectionState?.previewTopicId
    || (existingDetails.length > 0
      ? existingDetails[0].id
      : ''),
  )
  const [selectedTopic, setSelectTopic] = useState<string>(
    storeInstance?.stateStore?.trendSelectionState?.selectedTopicId
    || (existingDetails.length > 0
      ? existingDetails[0].id
      : ''), // 默认选择第一个热点话题
  ) // 选择为我的热点话题的ID
  const [selectedRednoteId, setSelectedRednoteId] = useState<string>('') // 选择的小红书笔记ID
  const [showRednoteList, setShowRednoteList] = useState<boolean>(storeInstance?.stateStore?.trendSelectionState?.showRednoteList ?? false) // 默认不显示，等待工作流触发后显示
  const [darenListLoaded, setDarenListLoaded] = useState<boolean>(true) // 默认为 true，让 RednoteReferenceList 立即加载数据
  const requestIdRef = useRef<string>('') // 使用 ref 存储请求标识，避免组件重新渲染导致的重复请求
  const hasInitializedRef = useRef<boolean>(false) // 使用 ref 追踪是否已初始化，组件卸载重新挂载也不会重置
  const hasFetchedDarenList = useRef<boolean>(false) // 标记是否已经请求过 distill_daren_list
  const hasFetchedHotpots = useRef<boolean>(false) // 标记是否已经请求过 hotpots_analysis
  const hasProcessedHotpotsData = useRef<boolean>(false) // 标记是否已经处理过 hotpots 数据

  /** 🎯 使用数据同步管理器监听状态变化 - 更可靠的通信机制 */
  useEffect(() => {
    if (!storeInstance?.dataSyncManager) {
      console.warn('[TrendSelectionPage] dataSyncManager 不可用，回退到传统监听方式')

      /** 回退到原有的监听方式 */
      const isCompleted = stateSnapshot.operationsThinking1Completed
      if (isCompleted) {
        setOperationsThinkingCompleted(true)
        setIsLoading(false)
      }
      else {
        setOperationsThinkingCompleted(false)
        setIsLoading(true)
      }
      return
    }

    const { dataSyncManager } = storeInstance

    /** 检查是否已经完成 */
    if (dataSyncManager.isDataReady('operationsThinking')) {
      console.warn('[TrendSelectionPage] 数据已准备完成，立即切换到实际内容')
      setOperationsThinkingCompleted(true)
      setIsLoading(false)
      return
    }

    /** 注册数据准备完成的回调 */
    const unsubscribe = dataSyncManager.onDataReady('operationsThinking', () => {
      console.warn('[TrendSelectionPage] 收到数据准备完成通知，切换到实际内容')
      setOperationsThinkingCompleted(true)
      setIsLoading(false)
    })

    return unsubscribe
  }, [storeInstance?.dataSyncManager, stateSnapshot.operationsThinking1Completed])

  /** 处理默认选择状态的初始化 */
  useEffect(() => {
    if (selectedTopic && storeInstance?.stateStore?.trendSelectionState) {
      /** 如果有默认选择的话题，确保保存到store中 */
      storeInstance.stateStore.trendSelectionState.selectedTopicId = selectedTopic
      storeInstance.stateStore.trendSelectionState.selectionType = 'hotpots'
      /** 通知父组件 */
      handlerSelectTopic(selectedTopic)
    }
  }, [selectedTopic, storeInstance, handlerSelectTopic])

  const handlerPreviewTopic = (topicId: string) => {
    setPreviewTopicId(topicId)
    /** 保存到 store */
    if (storeInstance?.stateStore?.trendSelectionState) {
      storeInstance.stateStore.trendSelectionState.previewTopicId = topicId
    }
    const item = topicsDetails.find(item => item.id === topicId) as TrendDetail
    setPreview({
      ...item,
    })
    /** 选择话题时，自动滚动到右侧详情区域（移动端） */
    if (window.innerWidth < 1024) {
      setTimeout(() => {
        const detailElement = document.getElementById('trend-detail-section')
        detailElement?.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }, 100)
    }
  }

  /** 检查并更新 Start content creation 按钮显示状态 */
  const checkAndUpdateContentCreationButton = () => {
    /** 检查 hotpots_analysis 是否完成 */
    const hasHotpotsData = storeInstance?.stateStore?.hotpotsTopics
      && Object.keys(storeInstance.stateStore.hotpotsTopics).length >= 10 // 5个topic + 5个detail

    /** 检查 distill_daren_list 是否完成 */
    const hasDistillData = storeInstance?.stateStore?.distillDarenListData
      && (storeInstance.stateStore.distillDarenListData.KOC?.length > 0
        || storeInstance.stateStore.distillDarenListData.KOL?.length > 0
        || storeInstance.stateStore.distillDarenListData.Regulars?.length > 0)

    const shouldShowButton = hasHotpotsData && hasDistillData

    /** 更新按钮显示状态 */
    if (shouldShowButton && storeInstance?.stateStore) {
      storeInstance.stateStore.showContentCreationButton = true

      /** 触发一个事件通知其他组件更新 */
      window.dispatchEvent(new CustomEvent('contentCreationButtonReady'))
    }
  }

  const fetchHotPots = () => {
    /** 防止重复调用 - 检查本地和全局状态 */
    if (hasFetchedHotpots.current || storeInstance?.stateStore?.hasTriggeredHotpotsAnalysis) {
      return
    }

    /** 使用传入的 taskInstanceId 或从 store 获取 */
    const currentTaskInstanceId = taskInstanceId || storeInstance?.stateStore?.taskInstanceId

    if (!currentTaskInstanceId) {
      return
    }

    /** 标记已触发 - 同时设置本地和全局标记 */
    hasFetchedHotpots.current = true
    if (storeInstance?.stateStore) {
      storeInstance.stateStore.hasTriggeredHotpotsAnalysis = true
    }

    fetchEventSourceWithTimeout(`http://192.168.112.171:8080/api/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId: currentTaskInstanceId,
        platform: 'rednote',
        workflowName: 'hotpots_analysis',
        parameters: {
          platform: 'rednote',
          planning_report,
        },
      }),
      onmessage(ev) {
        if (!ev.data || ev.event === 'end')
          return

        const parsed = JSON.parse(ev.data)
        const topicTitleList = ['topic1', 'topic2', 'topic3', 'topic4', 'topic5']
        const topicDetailNodeTitle = ['topic1_detail', 'topic2_detail', 'topic3_detail', 'topic4_detail', 'topic5_detail']

        /** 存储 topics 数据到 store - 只更新 store，让 useEffect 处理渲染 */
        if (storeInstance && parsed.node_title && parsed.content) {
          if (topicTitleList.includes(parsed.node_title) || topicDetailNodeTitle.includes(parsed.node_title)) {
            if (!storeInstance.stateStore.hotpotsTopics) {
              storeInstance.stateStore.hotpotsTopics = {}
            }
            if (!storeInstance.stateStore.hotpotsTopics[parsed.node_title]) {
              storeInstance.stateStore.hotpotsTopics[parsed.node_title] = ''
            }
            storeInstance.stateStore.hotpotsTopics[parsed.node_title] += parsed.content

            /** 触发一个自定义事件，通知数据更新 */
            window.dispatchEvent(new CustomEvent('hotpotsDataUpdate', {
              detail: { nodeTitle: parsed.node_title, content: parsed.content },
            }))
          }
        }

        /**
         * 移除直接设置 state 的代码，让 useEffect 统一处理
         * 这样可以避免重复渲染和状态不一致
         */
      },
      onclose() {
        /**
         * 响应完成
         */

        /** 触发完成事件 */
        window.dispatchEvent(new CustomEvent('hotpotsComplete'))
      },
    })
  }

  /** 处理小红书笔记选择 */
  const handleRednoteSelect = (noteId: string, checked: boolean) => {
    if (checked) {
      setSelectedRednoteId(noteId)
      /** 清除话题选择 */
      setSelectTopic('')
      if (storeInstance?.stateStore?.trendSelectionState) {
        storeInstance.stateStore.trendSelectionState.selectedTopicId = ''
        storeInstance.stateStore.trendSelectionState.selectedTopicTitle = '' // 清除话题标题
        storeInstance.stateStore.trendSelectionState.selectedRednoteId = noteId
        storeInstance.stateStore.trendSelectionState.selectionType = 'daren' // 标记选择类型为小红书
      }

      /** 同步更新 RednoteReferenceList 的选择状态 */
      if (storeInstance?.stateStore?.distillDarenListData) {
        storeInstance.stateStore.distillDarenListData.selectNoteId = noteId
      }

      /** 获取小红书笔记的完整数据 */
      let noteData = null
      try {
        /** 从 distillDarenListData 中获取笔记数据 */
        const distillData = storeInstance?.stateStore?.distillDarenListData
        if (distillData) {
          /** 在所有分类中查找对应的笔记 */
          const allNotes = [
            ...(distillData.KOC || []),
            ...(distillData.KOL || []),
            ...(distillData.Regulars || []),
            ...(distillData.All || []),
          ]

          const selectedNote = allNotes.find(note => note.id === noteId)
          if (selectedNote) {
            /** 提取图片信息 - 支持多种图片字段格式 */
            const imageUrl = selectedNote.image?.[0]
              || selectedNote.imageList?.[0]
              || selectedNote.imgList?.[0]
              || selectedNote.cover
              || 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop'

            /** 提取用户信息 */
            const userName = selectedNote.user?.name
              || selectedNote.nick
              || selectedNote.author
              || 'Unknown User'

            /** 提取互动数据 */
            const likes = selectedNote.stats?.likes
              || selectedNote.like
              || selectedNote.likeCount
              || '0'

            const comments = selectedNote.stats?.comm
              || selectedNote.comm
              || selectedNote.commentCount
              || '0'

            const reads = selectedNote.stats?.read
              || selectedNote.read
              || selectedNote.readCount
              || '0'

            noteData = {
              id: noteId,
              title: selectedNote.title || selectedNote.desc || '',
              content: selectedNote.desc || selectedNote.title || '',
              author: userName,
              noteLink: selectedNote.noteLink || '',
              type: 'daren',
              /** 新增字段 */
              imageUrl,
              userInfo: {
                name: userName,
                avatar: selectedNote.user?.avatar || selectedNote.avatar || 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31aje2ouc6q0049042ub7u510c0c06r0?imageView2/2/w/120/format/webp|imageMogr2/strip',
              },
              socialStats: {
                likes,
                comments,
                reads,
                likesFormatted: typeof likes === 'string'
                  ? likes
                  : String(likes),
                commentsFormatted: typeof comments === 'string'
                  ? comments
                  : String(comments),
                readsFormatted: typeof reads === 'string'
                  ? reads
                  : String(reads),
              },
            }

            /** 将笔记标题保存到 trendSelectionState 中用于卡片显示 */
            if (storeInstance?.stateStore?.trendSelectionState && noteData?.title) {
              storeInstance.stateStore.trendSelectionState.selectedRednoteTitle = noteData.title
            }
          }
        }
      }
      catch (error) {
      }

      /** 通知 ChatPage 更新选择状态，携带完整数据 */
      window.dispatchEvent(new CustomEvent('trendSelectionChange', {
        detail: {
          type: 'daren',
          selectedId: noteId,
          data: noteData,
        },
      }))
    }
    else {
      setSelectedRednoteId('')
      if (storeInstance?.stateStore?.trendSelectionState) {
        storeInstance.stateStore.trendSelectionState.selectedRednoteId = ''
        storeInstance.stateStore.trendSelectionState.selectionType = ''
      }

      /** 清除 RednoteReferenceList 的选择状态 */
      if (storeInstance?.stateStore?.distillDarenListData) {
        storeInstance.stateStore.distillDarenListData.selectNoteId = ''
      }

      /** 通知 ChatPage 更新选择状态 */
      window.dispatchEvent(new CustomEvent('trendSelectionChange', {
        detail: { type: '', selectedId: '', data: null },
      }))
    }
  }

  const handleReferenceToggle = (e: CheckboxChangeEvent, id: string) => {
    const { checked } = e.target
    const newSelectedId = checked
      ? id
      : ''
    handlerSelectTopic(newSelectedId) // 回传给上层
    setSelectTopic(newSelectedId)

    /** 如果选择了话题，清除小红书的选择 */
    if (checked) {
      setSelectedRednoteId('')

      /** 通知 RednoteReferenceList 清除选择状态 */
      if (storeInstance?.stateStore?.distillDarenListData) {
        storeInstance.stateStore.distillDarenListData.selectNoteId = ''
      }
    }

    /** 保存到 store */
    if (storeInstance?.stateStore?.trendSelectionState) {
      storeInstance.stateStore.trendSelectionState.selectedTopicId = newSelectedId
      storeInstance.stateStore.trendSelectionState.selectedRednoteId = '' // 清除小红书选择
      storeInstance.stateStore.trendSelectionState.selectionType = checked
        ? 'hotpots'
        : '' // 标记选择类型
    }

    /** 获取完整的话题数据 */
    let topicData = null
    if (checked && id) {
      const topicDetail = topicsDetails.find(topic => topic.id === id)
      const topicTitle = topics.find(topic => topic.id === id)

      topicData = {
        id,
        title: topicTitle?.title || '',
        content: topicDetail?.content || '',
        type: 'hotpots',
      }
    }

    /** 通知 ChatPage 更新选择状态，携带完整数据 */
    window.dispatchEvent(new CustomEvent('trendSelectionChange', {
      detail: {
        type: checked
          ? 'hotpots'
          : '',
        selectedId: newSelectedId,
        data: topicData,
      },
    }))

    console.log('[TrendSelectionPage] 选择了话题 ID:', newSelectedId, '选择状态:', checked)
  }

  useEffect(() => {
    /** 移除这部分重复的逻辑，因为已经在事件监听器中处理了 */

    /** 只有当有 taskInstanceId 和 planning_report 时才处理 */
    if (taskInstanceId && planning_report) {
      /** 检查是否是第一次触发（通过全局状态判断） */
      const shouldTriggerWorkflows = storeInstance?.stateStore?.hasTriggeredTrendWorkflows && !hasInitializedRef.current

      if (shouldTriggerWorkflows) {
        hasInitializedRef.current = true

        /** 检查 hotpots_analysis 是否已经在 ChatPage 中触发 */
        if (storeInstance?.stateStore?.hasTriggeredHotpotsAnalysis) {
        }
        else if (!storeInstance?.stateStore?.hotpotsTopics || Object.keys(storeInstance.stateStore.hotpotsTopics).length === 0) {
          /** 只有在没有数据且没有触发过的情况下才调用 */
          fetchHotPots()
        }
        else {
        }

        /** RednoteReferenceList 默认就显示，不需要特殊设置 */
        if (!hasFetchedDarenList.current) {
          hasFetchedDarenList.current = true
        }
      }
      else if (hasInitializedRef.current) {
        /** RednoteReferenceList 默认就显示，不需要特殊设置 */
        // setShowRednoteList(true) // 不需要，因为默认就是 true
        /** 不再设置 darenListLoaded，因为已经加载过了 */

        /** 如果已有数据，恢复显示 */
        if (storeInstance?.stateStore?.hotpotsTopics?.topic1) {
          /** 恢复 topics 数据 */
          const restoredTopics: TrendTopic[] = []
          const restoredDetails: Omit<TrendDetail, 'isReference'>[] = []

          for (let i = 1; i <= 5; i++) {
            const topicKey = `topic${i}` as keyof typeof storeInstance.stateStore.hotpotsTopics
            const detailKey = `topic${i}_detail` as keyof typeof storeInstance.stateStore.hotpotsTopics

            if (storeInstance.stateStore.hotpotsTopics[topicKey]) {
              restoredTopics.push({
                id: topicKey,
                title: storeInstance.stateStore.hotpotsTopics[topicKey],
              })
            }

            if (storeInstance.stateStore.hotpotsTopics[detailKey]) {
              restoredDetails.push({
                id: topicKey,
                content: storeInstance.stateStore.hotpotsTopics[detailKey],
              })
            }
          }

          if (restoredTopics.length > 0) {
            setTopics(restoredTopics)
            setTopicsDetails(restoredDetails)
          }
        }
      }
    }
  }, [
    taskInstanceId,
    planning_report,
    storeInstance,
  ])

  /** 监听 hotpotsTopics 数据变化（通过 useSnapshot） */
  useEffect(() => {
    if (stateSnapshot?.hotpotsTopics) {
      const hotpotsTopics = stateSnapshot.hotpotsTopics as any
      const snapTopics: TrendTopic[] = []
      const snapDetails: Omit<TrendDetail, 'isReference'>[] = []

      for (let i = 1; i <= 5; i++) {
        const topicKey = `topic${i}`
        const detailKey = `topic${i}_detail`

        if (hotpotsTopics[topicKey]) {
          snapTopics.push({
            id: topicKey,
            title: hotpotsTopics[topicKey],
          })

          snapDetails.push({
            id: topicKey,
            content: hotpotsTopics[detailKey] || 'Loading details...',
          })
        }
      }

      if (snapTopics.length > 0) {
        setTopics(snapTopics)
        setTopicsDetails(snapDetails)

        /** 设置预览并默认选中第一个话题 */
        if (!preview && snapDetails.length > 0) {
          const firstDetail = snapDetails[0]
          setPreview({
            id: firstDetail.id,
            content: firstDetail.content,
            isReference: false,
          })
          setPreviewTopicId(firstDetail.id)

          /** 默认选中第一个话题 */
          if (!selectedTopic) {
            setSelectTopic(firstDetail.id)
            handlerSelectTopic(firstDetail.id) // 回传给上层

            /** 保存到 store */
            if (storeInstance?.stateStore?.trendSelectionState) {
              storeInstance.stateStore.trendSelectionState.selectedTopicId = firstDetail.id
              storeInstance.stateStore.trendSelectionState.selectionType = 'hotpots'
            }
          }
        }
        else if (preview && previewTopicId) {
          /** 更新当前预览 */
          const currentDetail = snapDetails.find(d => d.id === previewTopicId)
          if (currentDetail && currentDetail.content !== preview.content) {
            setPreview({
              id: currentDetail.id,
              content: currentDetail.content,
              isReference: false,
            })
          }
        }
      }
    }
  }, [
    stateSnapshot?.hotpotsTopics?.topic1,
    stateSnapshot?.hotpotsTopics?.topic2,
    stateSnapshot?.hotpotsTopics?.topic3,
    stateSnapshot?.hotpotsTopics?.topic4,
    stateSnapshot?.hotpotsTopics?.topic5,
    stateSnapshot?.hotpotsTopics?.topic1_detail,
    stateSnapshot?.hotpotsTopics?.topic2_detail,
    stateSnapshot?.hotpotsTopics?.topic3_detail,
    stateSnapshot?.hotpotsTopics?.topic4_detail,
    stateSnapshot?.hotpotsTopics?.topic5_detail,
  ])

  /** 监听 hotpots 数据更新事件 */
  useEffect(() => {
    const handleHotpotsUpdate = (event: CustomEvent) => {
      /** 强制触发组件更新 */
      if (storeInstance?.stateStore?.hotpotsTopics) {
        const hotpotsTopics = storeInstance.stateStore.hotpotsTopics
        const newTopics: TrendTopic[] = []
        const newDetails: Omit<TrendDetail, 'isReference'>[] = []

        for (let i = 1; i <= 5; i++) {
          const topicKey = `topic${i}`
          const detailKey = `topic${i}_detail`

          if (hotpotsTopics[topicKey]) {
            newTopics.push({
              id: topicKey,
              title: hotpotsTopics[topicKey],
            })

            newDetails.push({
              id: topicKey,
              content: hotpotsTopics[detailKey] || 'Loading details...',
            })
          }
        }

        if (newTopics.length > 0) {
          setTopics(newTopics)
          setTopicsDetails(newDetails)

          /** 更新预览内容 */
          if (previewTopicId) {
            const updatedDetail = newDetails.find(d => d.id === previewTopicId)
            if (updatedDetail) {
              setPreview({
                id: updatedDetail.id,
                content: updatedDetail.content,
                isReference: false,
              })
            }
          }
          else if (!preview && newDetails.length > 0) {
            const firstDetail = newDetails[0]
            setPreview({
              id: firstDetail.id,
              content: firstDetail.content,
              isReference: false,
            })
            setPreviewTopicId(firstDetail.id)

            /** 默认选中第一个话题 */
            if (!selectedTopic) {
              setSelectTopic(firstDetail.id)
              handlerSelectTopic(firstDetail.id) // 回传给上层

              /** 保存到 store */
              if (storeInstance?.stateStore?.trendSelectionState) {
                storeInstance.stateStore.trendSelectionState.selectedTopicId = firstDetail.id
                storeInstance.stateStore.trendSelectionState.selectionType = 'hotpots'
              }

              /** 通知 ChatPage 更新选择状态 */
              window.dispatchEvent(new CustomEvent('trendSelectionChange', {
                detail: { type: 'hotpots', selectedId: firstDetail.id },
              }))
            }
          }
        }
      }
    }

    const handleHotpotsComplete = () => {
      checkAndUpdateContentCreationButton()
    }

    window.addEventListener('hotpotsDataUpdate', handleHotpotsUpdate as EventListener)
    window.addEventListener('hotpotsComplete', handleHotpotsComplete)

    return () => {
      window.removeEventListener('hotpotsDataUpdate', handleHotpotsUpdate as EventListener)
      window.removeEventListener('hotpotsComplete', handleHotpotsComplete)
    }
  }, [previewTopicId, preview, storeInstance])

  /** 监听 store 中 showRednoteList 的变化 */
  useEffect(() => {
    if (storeInstance?.stateStore?.trendSelectionState?.showRednoteList !== undefined) {
      const shouldShow = storeInstance.stateStore.trendSelectionState.showRednoteList
      setShowRednoteList(shouldShow)
    }
  }, [storeInstance?.stateStore?.trendSelectionState?.showRednoteList])

  /** 监听 distillDarenListData 数据变化，确保数据到达后立即显示小红书列表 */
  useEffect(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      const { KOC, KOL, Regulars, loading } = storeInstance.stateStore.distillDarenListData
      const hasData = (KOC?.length > 0 || KOL?.length > 0 || Regulars?.length > 0)

      /** 如果有数据且不在加载中，自动显示小红书列表 */
      if (hasData && !loading) {
        if (!showRednoteList) {
          setShowRednoteList(true)
        }

        /** 检查并更新 Start content creation 按钮状态 */
        checkAndUpdateContentCreationButton()
      }
    }
  }, [
    storeInstance?.stateStore?.distillDarenListData?.KOC?.length,
    storeInstance?.stateStore?.distillDarenListData?.KOL?.length,
    storeInstance?.stateStore?.distillDarenListData?.Regulars?.length,
    storeInstance?.stateStore?.distillDarenListData?.loading,
  ])

  /** 🎯 根据 loading 状态决定显示骨架屏还是实际内容 */
  if (isLoading || !operationsThinkingCompleted) {
    return (
      <div className="fade-in">
        <TrendSelectionSkeleton />
      </div>
    )
  }

  return (
    <div className={ cn('w-full max-w-7xl mx-auto p-6 space-y-8 relative fade-in', className) }>

      {/* 关闭按钮 */}
      {onClose && (
        <button
          onClick={ onClose }
          className="absolute right-4 top-4 rounded-full bg-gray-100 p-2 transition-colors duration-200 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"
          aria-label="Close"
        >
          <X className="h-5 w-5 text-gray-600 dark:text-gray-300" />
        </button>
      )}

      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-2xl text-gray-900 font-semibold dark:text-gray-100">
          Choose either one trending topic or a reference post
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          We will create your posts based on your choice
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Left Side - Trending Topics */}
        <div className="space-y-4">

          <div className="space-y-3">
            {topics.length === 0
              ? (
                  <div className="py-8 text-center text-gray-500">
                    Loading trending topics...
                  </div>
                )
              : topics.map((topic, index) => (
                  <motion.div
                    key={ topic.id }
                    initial={ { opacity: 0, y: 20 } }
                    animate={ { opacity: 1, y: 0 } }
                    transition={ { duration: 0.3, delay: index * 0.1 } }
                    className={ clsx(`[background-clip:padding-box,border-box] [background-origin:padding-box,border-box] relative h-[74px] cursor-pointer overflow-hidden border-[1.5px] border-[#D9D9D9] rounded-[8px] transition-all duration-300 hover:border-[#DD9DFF] hover:shadow-md`, previewTopicId === topic.id && 'border-[#DD9DFF] shadow-md') }
                    onClick={ () => handlerPreviewTopic(topic.id) }
                  >
                    {/* {selectedTopic === topic.id && <span className="absolute right-[12px] top-[12px] items-center rounded-full bg-gray-100 px-2 py-0.5 text-sm text-gray-700">
                      Selected
                    </span>} */}
                    <div
                      className="relative ml-[14px] flex items-center justify-between pr-[14px] leading-[74px] transition-all duration-300"
                    >
                      <h3 className="mb-1 flex-1 pr-2 text-gray-900 font-medium dark:text-gray-100">
                        {topic.title}
                      </h3>
                      {storeInstance?.stateStore?.trendSelectionState?.selectedTopicId === topic.id && (
                        <span className="flex-shrink-0 border border-blue-200 rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-700 font-medium">
                          Selected
                        </span>
                      )}
                    </div>
                  </motion.div>
                ))}
          </div>
        </div>

        {/* Right Side - Trend Detail */}
        <div id="trend-detail-section" className="space-y-4">
          {!preview ? (
            <div className="[background-clip:padding-box,border-box] [background-origin:padding-box,border-box] relative h-[410px] flex items-center justify-center overflow-hidden border-[1.5px] border-[#D9D9D9] rounded-2xl p-[16px]">
              <p className="text-gray-500">Select a topic to preview details</p>
            </div>
          ) : (
            <motion.div
              initial={ { opacity: 0, x: 20 } }
              animate={ { opacity: 1, x: 0 } }
              transition={ { duration: 0.4 } }
              className="[background-clip:padding-box,border-box] [background-image:linear-gradient(to_right,#fff,#fff),linear-gradient(90deg,#DD9DFF_0%,#36D3FF_100%)] [background-origin:padding-box,border-box] relative overflow-hidden border-[1.5px] border-transparent rounded-2xl transition-all duration-300"
            >
              <div
                className="h-[410px] p-[16px] transition-all duration-300 space-y-4"
                style={ {
                  background: preview.id === selectedTopic
                    ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
                    : 'transparent',
                } }
              >
                <div className="h-[calc(100%-39px)] overflow-auto space-y-3">
                  <p className="text-gray-700 leading-relaxed dark:text-gray-300">
                    {preview.content}
                  </p>
                </div>

                {/* 复选框在面板内部 */}
                <div className="w-full flex items-center justify-end gap-3 dark:border-gray-600">
                  <Checkbox onChange={ e => handleReferenceToggle(e, preview.id) } checked={ preview.id === selectedTopic }>Select as my reference</Checkbox>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* 小红书参考列表 - 始终渲染但可能隐藏 */}
      <motion.div
        initial={ { opacity: 0, y: 20 } }
        animate={ { opacity: showRednoteList
          ? 1
          : 0, y: showRednoteList
          ? 0
          : 20 } }
        transition={ { duration: 0.5 } }
        className="mt-8"
        style={ { display: showRednoteList
          ? 'block'
          : 'none' } }
      >
        <RednoteReferenceList
          handleSelectNote={ (noteId: string, checked: boolean) => {
            handleRednoteSelect(noteId, checked)
          } }
          selectedNoteId={ selectedRednoteId }
          useDynamicData
          taskInstanceId={ taskInstanceId }
          shouldFetch={ darenListLoaded }
          storeInstance={ storeInstance }
        />
      </motion.div>

    </div>
  )
}

export default TrendSelectionPage
