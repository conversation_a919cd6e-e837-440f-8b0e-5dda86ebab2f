import type { CSSProperties } from 'react'
import classnames from 'clsx'
import { memo } from 'react'

function _Agree({
  style,
  className,
}: AgreeProps) {
  return <div
    className={ classnames(
      'mt-4 px-4 text-center text-sm text-light',
      className,
    ) }
    style={ style }
  >
    By continuing, you agree to our
    <a
      target="_blank"
      className="cursor-pointer text-xs text-primary font-bold"
      href="/terms-of-service.html"
    >
      &nbsp; Terms of Service &nbsp;
    </a>

    and acknowledge our &nbsp;

    <a
      target="_blank"
      className="cursor-pointer text-xs text-primary font-bold"
      href="/privacy-policy.html"
    >
      Privacy Policy
    </a>
    .
  </div>
}

export const Agree = memo<AgreeProps>(_Agree)
Agree.displayName = 'Agree'

export type AgreeProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode
}
