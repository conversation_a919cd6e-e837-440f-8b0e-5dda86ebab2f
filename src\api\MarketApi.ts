import type { Optional } from '@jl-org/ts-tool'
import type { PageQuery, PagerList } from '@/dts'
import { downloadByData, isStr, wait } from '@jl-org/tool'
import { http, request } from '@/utils'

const mockModules = import.meta.glob<any>('./marketTestData/step*.json')

export class MarketApi {
  /** 步骤 id */
  executionId = ''
  /** 实例 id */
  processInstanceId = ''

  static isMock = false

  static async getTreeList(): Promise<XhsCategoryReq[]> {
    return request.get('/app/xhs-category/tree')
  }

  async executeAndStream(
    data: MarketWorkflowReq,
    onMsg?: (data: MarketApiResult) => void,
  ): Promise<MarketApiResult> {
    const { promise, reject, resolve } = Promise.withResolvers<MarketApiResult>()

    if (MarketApi.isMock) {
      const { workflowStep } = data
      const mockData = await mockModules[`./marketTestData/${workflowStep}.json`]()
      const result: MarketApiResult = {
        data: {},
        allJson: mockData.allJson,
        hasError: false,
        errorMsg: '',
      }

      const waitSteps = [
        'step1',
        'step2',
        'step3',
        'step4',
        'step5',
        'step6',
        'step7',
        'step8',
      ]

      for (let i = 0; i < mockData.allJson.length; i++) {
        const item = mockData.allJson[i]
        if (!isStr(item) && item.__internal__event === 'Message') {
          const currentContent = result.data[item.node_title]?.content || ''
          result.data[item.node_title] = {
            content: currentContent + item.content,
          }

          waitSteps.includes(workflowStep) && await wait(1)
          onMsg?.(result)
        }
        else if (!isStr(item) && item.__internal__event === 'Error') {
          result.hasError = true
          result.errorMsg = item.details.error_message

          onMsg?.(result)
          resolve(result)
          return promise
        }
      }
      resolve(result)

      return promise
    }

    let needRecordMeta = true
    const result: MarketApiResult = {
      data: {},
      allJson: [],
      hasError: false,
      errorMsg: '',
    }

    const body: MarketWorkflowReq = {
      processInstanceId: this.processInstanceId,
      ...data,
    }

    try {
      const { promise: ssePromise } = await http.fetchSSE('/app/market/stream/xhs', {
        onMessage: ({ currentJson, allJson }) => {
          if (needRecordMeta) {
            const data = currentJson as Msg[]
            for (const item of data) {
              if (!isStr(item) && item.__internal__event === 'meta') {
                this.executionId = item.executionId
                this.processInstanceId = item.processInstanceId
                needRecordMeta = false
              }
              else if (!isStr(item) && item.__internal__event === 'Error') {
                result.hasError = true
                result.errorMsg = item.details.error_message

                onMsg?.(result)
                // resolve(result)
                // return promise
              }
            }
          }

          for (const item of (currentJson || [])) {
            if (!isStr(item) && item.__internal__event === 'Message') {
              const currentContent = result.data[item.node_title]?.content || ''
              result.data[item.node_title] = {
                content: currentContent + item.content,
              }
            }
            else if (!isStr(item) && item.__internal__event === 'Error') {
              result.hasError = true
              result.errorMsg = item.details.error_message

              onMsg?.(result)
              // resolve(result)
              // return promise
            }
          }

          result.allJson = allJson as Msg[]
          onMsg?.(result)
        },
        method: 'POST',
        needParseData: true,
        needParseJSON: true,
        body,
      })

      await ssePromise
      resolve(result)
    }
    catch (error) {
      console.error(error)
      result.hasError = true
      resolve(result)
    }

    console.log(result)
    MarketApi.saveTestResultToFile(result, `${data.workflowStep}.json`)
    return promise
  }

  async updateRelatedMarketReport(
    data: Optional<MarketUpdateRelateTextReq, 'executionId' | 'processInstanceId'>,
  ): Promise<void> {
    if (MarketApi.isMock) {
      return
    }

    const body = {
      processInstanceId: this.processInstanceId,
      executionId: this.executionId,
      ...data,
    }

    return request.post('/app/market/xhs/up-report', body)
  }

  static async listXhsHistory(
    params: PageQuery,
  ): Promise<PagerList<MarketHistoryPageResp>> {
    return request.get('/app/market/xhs/list', { params })
  }

  static async listXhsHistoryDetail(
    params: PageQuery<{ processInstanceId: string }>,
  ): Promise<PagerList<MarketHistoryPageResp>> {
    return request.get('/app/market/xhs/list/detail', { params })
  }

  /**
   * 获取任务详情
   */
  static async getTaskDetail(taskInstanceId: string): Promise<MarketTaskDetailResp> {
    // 添加缓存控制头强制刷新
    return request.get(`/app/market/task-detail/${taskInstanceId}`, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }

  /**
   * @deprecated
   */
  async confirm(
    data?: Partial<MarketConfirmReq>,
  ): Promise<void> {
    return
    if (MarketApi.isMock) {
      return
    }

    return request.post('/app/market/xhs/confirm', {
      processInstanceId: this.processInstanceId,
      executionId: this.executionId,
      ...data,
    })
  }

  static async saveTestResultToFile(data: any, filename = 'market-api-test-result.json') {
    try {
      const jsonData = JSON.stringify(data, null, 2)
      downloadByData(jsonData, filename)
      return true
    }
    catch (error) {
      console.error('保存测试结果失败:', error)
      return false
    }
  }

  async testStepExecution(step: StepNum, saveResult = true) {
    const marketTestData = await import('./marketTestData/test.data')

    const params: Record<string, any> = {
      industry_id: 83,

      industry: '',
      product_name: '',
      ip: '',
      brand: '',
      role: '',
      product: '',
      pic: '',
      company: '',
    }

    /** 根据步骤准备不同的参数 */
    switch (step) {
      case 'step1':
        break
      case 'step2':
        params.brand_report = marketTestData.brand_report
        params.industry_report = marketTestData.industry_report
        break
      case 'step3':
        break
      case 'step4':
        params.biji_data = marketTestData.biji_data
        params.brand_data = marketTestData.brand_data
        params.brand_report = marketTestData.brand_report
        params.competitor_data = marketTestData.competitor_data
        params.daren_data = marketTestData.daren_data
        params.industry_data = marketTestData.industry_data
        params.industry_report = marketTestData.industry_report
        params.product_data = marketTestData.product_data
        break
      case 'step5':
        params.brand_market_report = `${marketTestData.research_content}\n\n${marketTestData.research_content1}\n\n${marketTestData.research_content2}`
        break
      case 'step6':
        params.biji_data = marketTestData.biji_data
        params.brand_market_report = `${marketTestData.research_content}\n\n${marketTestData.research_content1}\n\n${marketTestData.research_content2}`
        params.daren_data = marketTestData.daren_data
        break
      case 'step7':
        params.biji_url = 'https://www.xiaohongshu.com/explore/686a4c2f00000000230040ba?xsec_token=AB8uf-GkhMqUPWScTTKeLavbe03BwB816Su33DVZAA14g='
        break
      case 'step8':
        params.biji_content = marketTestData.biji_content
        params.biji_title = marketTestData.biji_title
        params.daren_list = marketTestData.daren_list
        params.biji_url = 'https://www.xiaohongshu.com/explore/686a4c2f00000000230040ba?xsec_token=AB8uf-GkhMqUPWScTTKeLavbe03BwB816Su33DVZAA14g='
        break
    }

    const testData: MarketWorkflowReq = {
      processInstanceId: step === 'step1'
        ? ''
        : this.processInstanceId || '1942185758874013696',
      workflowStep: step,
      // @ts-ignore
      params,
    }

    const result = await this.executeAndStream(testData)
    const allData = result.allJson as unknown as Msg[]

    /** 如果需要保存结果 */
    if (saveResult && allData.length > 0) {
      await MarketApi.saveTestResultToFile(result, `market-api-${step}-result.json`)
      Object.entries(result.data).forEach(([k, v]) => {
        if (k === 'allJson') {
          return
        }

        const content = v.content
        MarketApi.saveTestResultToFile(content, `market-api-${step}-${k}.markdown`)
      })
    }

    return result
  }
}

// window.m = new MarketApi()

// window.test = async () => {
//   const res = await MarketApi.listXhsHistory({
//     page: 1,
//     size: 10,
//   })
//   const id = res.list[0].processInstanceId!

//   const data = await MarketApi.listXhsHistoryDetail({
//     processInstanceId: id,
//     page: 1,
//     size: 10,
//   })

//   console.log(data)
// }

export type MarketHistoryPageResp = {
  id: string
  /** 创建人 */
  createUserString: string
  /** 创建时间 */
  createTime: string
  /** 是否禁用修改 */
  disabled: boolean
  /** 流程实例ID */
  processInstanceId?: string
  /** 流程步骤id */
  workflowStep: string
  /** 角色：1:USER , 2:AI */
  role: string
  /** 单次执行id */
  executionId: string
  /** 生成内容-对应不同的工作流情况 */
  contentPayload: Record<string, unknown>
}

export type MarketApiResult = {
  data: {
    [key: string]: {
      content: string
    }
  }
  hasError: boolean
  errorMsg: string
  allJson: Msg[]
}

export type XhsCategoryReq = {
  label: string
  parentId?: number
  tagId: number
  subTagList?: XhsCategoryReq[]
}

export type StepNum = `step${number}`

export type MarketWorkflowReq = {
  /** 流程实例ID */
  processInstanceId?: string
  workflowStep: StepNum
  params?: MarketWorkflowParams
}

export type MarketUpdateRelateTextReq = {
  /** 流程实例ID */
  processInstanceId: string
  /** 步骤 id */
  executionId: string
  /** 具体修改的内容，形式跟返回一样，eg："industry_report":"# 彩妆专题研究报告...." */
  params?: any
}

/**
 * @link {@see https://jvp4cecg8sw.feishu.cn/docx/HUzNdr4FEo7MXAxceq9cRJj1nqd}
 */
export type MarketWorkflowParams
  = | MarketStep1Params
    | MarketStep2Params
    | MarketStep3Params
    | MarketStep4Params
    | MarketStep5Params
    | MarketStep6Params
    | MarketStep7Params
    | MarketStep8Params

export type ServerSentEventObject = Record<string, any>

export type MarketStep1Params = {
  industry: string
  /**
   * 行业ID
   */
  industry_id: number
  product_name: string
  ip: string
  brand: string
  role: string
  product: string
  pic: string
  marketing_strategy?: string
  product_market?: string
  competitor?: string
  competitor_info?: string
  company?: string
  /**
   * 账户链接
   */
  account_link?: string
}

export type MarketStep2Params = {
  brand_report: string
  industry_report: string
  brand: string
}

export type MarketStep3Params = {
  industry: string
  brand: string
  product_name: string
  competitor?: string
  industry_id: number
}

export type MarketStep4Params = {
  biji_data: any
  brand: string
  brand_data: any
  brand_report: any
  company?: string
  competitor_info?: string
  competitor_data: any
  daren_data: any
  industry: string
  industry_data: any
  industry_report: any
  ip?: string
  marketing_strategy?: string
  product_data: any
  product: string
  product_market?: string
  product_name: string
  role?: string
}

export type MarketStep5Params = {
  brand_market_report: string
}

export type MarketStep6Params = {
  biji_data: any
  brand_market_report: any
  daren_data: any
  product: string
  industry: string
  product_name: string
}

export type MarketStep7Params = {
  biji_url: string
}

export type MarketStep8Params = {
  biji_content: string
  biji_title: string
  daren_list: any[]
  ip?: string
  pic?: string
  product?: string
  role?: string
  biji_url: string
}

export type MarketConfirmReq = {
  /** 流程实例ID */
  processInstanceId: string
  /** 要确认的具体执行ID */
  executionId: string
}

export type ContentMsg = {
  content: string
  node_title: string
  node_seq_id: string
  node_is_finish: boolean
  token: null
  ext: null
  __internal__event: 'Message'
}

export type ErrorMsg = {
  __internal__event: 'Error'
  details: {
    error_code: string
    error_message: string
  }
}

export type MetaMsg = {
  __internal__event: 'meta'
  executionId: string
  processInstanceId: string
}

export type Msg = ContentMsg | MetaMsg | ErrorMsg

// ======================
// * T
// ======================

/**
 * @example
 * {
    "coll": 14877,
    "comm": 5066,
    "desc": "当学会和婆婆“有效沟通”，有多爽 #剧情[话题]#   #家庭[话题]#   #生活[话题]#   #婆媳日长[话题]# ",
    "duration": null,
    "exp": 34873682,
    "fans": 167793,
    "like": 261077,
    "nick": "我有个我们",
    "noteLink": "https://www.xiaohongshu.com/explore/6861f01f00000000200199ac?xhsshare=CopyLink",
    "postTime": "2025-06-30 10:02:07",
    "read": 17704181,
    "share": 3958,
    "stat": 281020,
    "title": "",
    "topicList": null,
    "type": "video"
  }
 */
export type BiJiData = {
  coll: number
  comm: number
  desc: string
  duration: null
  exp: number
  fans: number
  like: number
  nick: string
  noteLink: string
  postTime: string
  read: number
  share: number
  stat: number
  title: string
  topicList: null
  type: string
}

/**
 * @example
 *  {
    "age": null,
    "anchorLink": "https://www.xiaohongshu.com/user/profile/5fb23b21000000000100bee3",
    "contact": null,
    "fansAdd30": 121441,
    "fansAdd7": 46386,
    "fansAdd90": 223799,
    "fansCount": 8013915,
    "followCount": 5,
    "likeCollCount": 183732954,
    "nick": "爆笑办公室",
    "noteAdd30": 89,
    "noteAdd7": 20,
    "noteAdd90": 251,
    "pictureCpe": 0,
    "pictureCpm": 0,
    "picturePrice": 39800,
    "redId": "1020781598",
    "tagList": null,
    "totalNoteCount": 3794,
    "type": "头部达人",
    "userSex": "F",
    "userText": "老板不在，单身女员工的奇葩日常！\n每周一、三、五，晚5点准时更新\n粉丝破百万，全体单身小姐姐直播相亲☺️",
    "videoCpe": 110,
    "videoCpm": 2643,
    "videoPrice": 39800
  }
 */
export type DaRenData = {
  age: null
  anchorLink: string
  contact: null
  fansAdd30: number
  fansAdd7: number
  fansAdd90: number
  fansCount: number
  followCount: number
  likeCollCount: number
  nick: string
  noteAdd30: number
  noteAdd7: number
  noteAdd90: number
  pictureCpe: number
  pictureCpm: number
  picturePrice: number
  redId: string
  tagList: null
  totalNoteCount: number
  type: string
  userSex: string
  userText: string
  videoCpe: number
  videoCpm: number
  videoPrice: number
}

/**
 * @example
 * {
    "auther_avatar": "https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo31e7sld2k0s0g48dfpsm3nlg1on9gss0?imageView2/2/w/80/format/jpg",
    "auther_home_page_url": "https://www.xiaohongshu.com/user/profile/574fec3b82ec392722e3d601",
    "auther_nick_name": "钱多多SARAH 造型师",
    "auther_user_id": "574fec3b82ec392722e3d601",
    "note_card_type": "normal",
    "note_cover_height": "1472",
    "note_cover_url_default": "http://sns-webpic-qc.xhscdn.com/202507081131/9b02f7a42f379ba23657d8bafc02ef75/1040g2sg31jjg15n52o1048dfpsm3nlg1akhbl98!nc_n_webp_mw_1",
    "note_cover_url_pre": "http://sns-webpic-qc.xhscdn.com/202507081131/a2cf55f87d0f3d052413352086a0dafa/1040g2sg31jjg15n52o1048dfpsm3nlg1akhbl98!nc_n_webp_prv_1",
    "note_cover_width": "1104",
    "note_display_title": "🔥10秒自测！黄皮/白皮？别再买错粉底液啦",
    "note_id": "686a4c2f00000000230040ba",
    "note_liked": false,
    "note_liked_count": "31",
    "note_model_type": "note",
    "note_url": "https://www.xiaohongshu.com/explore/686a4c2f00000000230040ba?xsec_token=AB8uf-GkhMqUPWScTTKeLavbe03BwB816Su33DVZAA14g=",
    "note_xsec_token": "AB8uf-GkhMqUPWScTTKeLavbe03BwB816Su33DVZAA14g="
  }
 */
export type BiJiList = {
  auther_avatar: string
  auther_home_page_url: string
  auther_nick_name: string
  auther_user_id: string
  note_card_type: string
  note_cover_height: string
  note_cover_url_default: string
  note_cover_url_pre: string
  note_cover_width: string
  note_display_title: string
  note_id: string
  note_liked: boolean
  note_liked_count: string
  note_model_type: string
  note_url: string
  note_xsec_token: string
}

export type DaRenLinkData = {
  info: any[]
  link: string
}

export type DarenList = {
  koc: DaRenLinkData[]
  kol: DaRenLinkData[]
  suren: DaRenLinkData[]
}

export type Step8Result = {
  content: Content
  title: Content
  image: Content
}

export type Content = {
  content: string
}

/** 任务详情响应类型 */
export type MarketTaskDetailResp = {
  success: boolean
  code: number
  msg: string
  data: MarketTaskDetailData
  timestamp: number
}

export type MarketTaskDetailData = {
  taskInstanceId: string
  chainId: string
  platform: 'rednote' | 'douyin' | 'taobao'
  taskStatus: 'PENDING' | 'INTENT_RECOGNIZING' | 'INTENT_RECOGNIZED' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  detectedIntent: 'product_marketing' | 'competitor_analysis' | 'market_research' | string
  inputImages?: string[]
  createTime: string
  updateTime: string
  formData?: Record<string, any>
  intentRecognitions?: MarketIntentRecognitionRecord[]
  insightReports?: MarketInsightReportGroup
  competitorReports?: MarketCompetitorReportGroup
  planningReports?: MarketPlanningReportGroup
  otherExecutions?: MarketOtherWorkflowExecution[]
}

export type MarketIntentRecognitionRecord = {
  executionId: string
  createTime: string
  sequence: number
  userInput: string
  userImages?: string[]
  aiResponse: string
  intentCode?: string
  intentRecognized: boolean
}

export type MarketInsightReportGroup = {
  reportType: string
  totalCount: number
  executions: MarketReportExecution[]
}

export type MarketCompetitorReportGroup = {
  reportType: string
  totalCount: number
  executions: MarketReportExecution[]
}

export type MarketPlanningReportGroup = {
  reportType: string
  totalCount: number
  executions: MarketReportExecution[]
}

export type MarketReportExecution = {
  executionId: string
  workflowName: string
  versionNumber: number
  isCurrentVersion: boolean
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED'
  createTime: string
  updateTime: string
  inputPayload?: Record<string, any>
  outputPayload?: Record<string, any>
  visualizationOutput?: Record<string, any>
  metadata?: Record<string, any>
}

export type MarketOtherWorkflowExecution = {
  executionId: string
  workflowName: string
  status: 'PENDING' | 'RUNNING' | 'SUCCESS' | 'FAILED'
  createTime: string
  updateTime: string
  inputPayload?: Record<string, any>
  outputPayload?: Record<string, any>
  metadata?: Record<string, any>
}
