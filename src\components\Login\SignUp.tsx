import type { CSSProperties } from 'react'
import { isMobileDevice } from '@/config'
import { Button, Form, Input, Typography } from 'antd'
import classnames from 'clsx'
import { Eye, EyeOff } from 'lucide-react'
import { memo } from 'react'
import { Agree } from './Agree'
import { useLogin } from './useLogin'

const { Text, Link } = Typography

function SignUpComponent({
  style,
  className,
  onSignIn,
}: SignUpProps) {
  const [form] = Form.useForm()
  const {
    loading,
    onEmailRegister,
    sendCode,
    disabled,
    time,
  } = useLogin(form)

  return <Form
    form={ form }
    name="signup"
    onFinish={ (value) => {
      onEmailRegister(value).then(() => {
        onSignIn?.()
      })
    } }
    className={ classnames(
      'flex flex-col',
      className,
    ) }
    style={ style }
  >
    <div className="h-12">
      <Text className="font-semibold !text-blue-500">Sign up for an account</Text>
    </div>

    <Form.Item
      name="email"
      rules={ [{ required: true, message: 'Please input your email!' }] }
    >
      <Input
        allowClear
        placeholder="Email"
        className="!h-10 !rounded-xl"
        data-id="signupEmail"
      />
    </Form.Item>

    <Form.Item
      name="password"
      rules={ [{ required: true, message: 'Please input your password!' }] }
    >
      <Input.Password
        allowClear
        placeholder="Password"
        className="!h-10 !rounded-xl"
        iconRender={ visible => (visible
          ? <Eye />
          : <EyeOff />) }
        data-id="signupPassword"
      />
    </Form.Item>

    <Form.Item
      name="confirm"
      dependencies={ ['password'] }
      rules={ [
        { required: true, message: 'Please confirm your password!' },
        ({ getFieldValue }) => ({
          validator(_, value) {
            if (!value || getFieldValue('password') === value) {
              return Promise.resolve()
            }
            return Promise.reject(new Error('The two passwords do not match!'))
          },
        }),
      ] }
      data-id="signupConfirm"
    >
      <Input.Password
        allowClear
        placeholder="Confirm"
        className="!h-10 !rounded-xl"
        iconRender={ visible => (visible
          ? <Eye />
          : <EyeOff />) } />
    </Form.Item>

    <div className="w-full flex gap-4">
      <Form.Item name="captcha" rules={ [{ required: true, message: 'Please input your verification code!' }] }>
        <Input
          allowClear
          placeholder="Verification Code"
          className="!h-10 !flex-1 !rounded-xl"
          data-id="signupCaptcha"
        />
      </Form.Item>
      <div
        className={ `h-10 w-32 flex cursor-pointer items-center justify-center rounded-xl bg-[#F2F3F6] px-4 text-center text-nowrap transition duration-300 !text-blue-500 hover:opacity-70 ${disabled && 'opacity-70 !cursor-not-allowed'}  ` }
        onClick={ sendCode }
      >
        {
          disabled
            ? time
            : 'Send Code'
        }
      </div>
    </div>

    <div className="mb-6">
      <div className="text-light">
        Already have an account? &nbsp;
        <Link className="text-blue-500" onClick={ onSignIn }>Login</Link>
      </div>
    </div>

    <Form.Item className="!mt-auto">
      <Button
        type="primary"
        htmlType="submit"
        className="w-full !h-10 !rounded-xl"
        loading={ loading }
        data-id="signupSubmit"
      >
        Sign Up
      </Button>
    </Form.Item>

    <Agree />
  </Form>
}

export const SignUp = memo<SignUpProps>(SignUpComponent)
SignUp.displayName = 'SignUp'

export type SignUpProps = {
  className?: string
  style?: CSSProperties
  children?: React.ReactNode

  onSignIn?: () => void
}
