import { But<PERSON>, Checkbox, Select } from 'antd'
import classNames from 'clsx'
import { ChevronDown, Eye, Heart, Link, MessageCircle } from 'lucide-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSnapshot } from 'valtio'
import { callDistillDarenListAPI, getWorkflowOutput } from '@/pages/Trend/stores/cozeStreamApi'
import { userStore } from '@/store/userStore'
import { fetchEventSourceWithTimeout } from '@/utils/streamTimeout'
import ImageCarousel from './ImageCarousel'
import ThumbnailCard from './ThumbnailCard'

/** 骨架屏组件 */
function RednoteReferenceSkeleton() {
  return (
    <div className="bg-white p-6">
      <style>
        {`
        @keyframes shimmer {
          0% { background-position: -200px 0; }
          100% { background-position: calc(200px + 100%) 0; }
        }
        .skeleton-shimmer {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200px 100%;
          animation: shimmer 1.5s infinite;
        }
        .fade-in {
          animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `}
      </style>

      {/* Header Skeleton */}
      <div className="mb-6">
        <div className="skeleton-shimmer mb-2 h-8 w-80 rounded"></div>
        <div className="skeleton-shimmer h-4 w-full rounded"></div>
        <div className="skeleton-shimmer mt-1 h-4 w-3/4 rounded"></div>
      </div>

      {/* Category Filter Skeleton */}
      <div className="flex items-center py-4">
        <div className="skeleton-shimmer mr-4 h-4 w-32 rounded"></div>
        <div className="skeleton-shimmer h-8 w-24 rounded-full"></div>
      </div>

      {/* Main Content Card Skeleton */}
      <div className="mb-2 h-134 flex overflow-hidden border-2 border-gray-100 rounded-xl">
        {/* Image Section Skeleton */}
        <div className="relative flex-1">
          <div className="skeleton-shimmer absolute inset-0"></div>
          {/* Image indicators skeleton */}
          <div className="absolute bottom-4 left-1/2 flex gap-2 -translate-x-1/2">
            {[...new Array(3)].map((_, i) => (
              <div key={ `indicator-${i}` } className="h-2 w-2 rounded-full bg-white/50"></div>
            ))}
          </div>
          {/* Image counter skeleton */}
          <div className="absolute right-4 top-4 h-6 w-12 rounded-full bg-black/20"></div>
        </div>

        {/* Content Section Skeleton */}
        <div className="flex flex-1 flex-col p-6">
          {/* User Info Skeleton */}
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center">
              <div className="skeleton-shimmer mr-2 h-8 w-8 rounded-full"></div>
              <div className="skeleton-shimmer mr-2 h-4 w-20 rounded"></div>
            </div>
            <div className="skeleton-shimmer h-6 w-12 rounded-xl"></div>
          </div>

          {/* Post Content Skeleton */}
          <div className="mb-4 flex-1">
            <div className="skeleton-shimmer mb-3 h-5 w-full rounded"></div>
            <div className="skeleton-shimmer mb-2 h-5 w-4/5 rounded"></div>
            <div className="skeleton-shimmer mb-3 h-4 w-full rounded"></div>
            <div className="skeleton-shimmer mb-2 h-4 w-full rounded"></div>
            <div className="skeleton-shimmer h-4 w-3/4 rounded"></div>
          </div>

          {/* Stats and Actions Skeleton */}
          <div className="mb-4 flex items-center justify-between border-t border-gray-200 pt-4">
            <div className="flex items-center gap-4">
              {[...new Array(3)].map((_, i) => (
                <div key={ `stat-${i}` } className="flex items-center gap-1">
                  <div className="skeleton-shimmer h-4 w-4 rounded"></div>
                  <div className="skeleton-shimmer h-4 w-8 rounded"></div>
                </div>
              ))}
            </div>
            <div className="skeleton-shimmer h-4 w-20 rounded"></div>
          </div>

          {/* Checkbox Skeleton */}
          <div className="flex items-center justify-end gap-2">
            <div className="skeleton-shimmer h-4 w-4 rounded"></div>
            <div className="skeleton-shimmer h-4 w-32 rounded"></div>
          </div>
        </div>
      </div>

      {/* Thumbnail Carousel Skeleton */}
      <div className="relative flex items-center gap-3">
        <div className="skeleton-shimmer h-8 w-8 rounded-full"></div>
        <div className="flex flex-1 gap-4 py-2">
          {[...new Array(5)].map((_, i) => (
            <div key={ `thumbnail-${i}` } className="skeleton-shimmer h-24 w-20 flex-shrink-0 rounded-lg"></div>
          ))}
        </div>
        <div className="skeleton-shimmer h-8 w-8 rounded-full"></div>
      </div>
    </div>
  )
}

/** 图片缓存管理 */
const imageCache = new Map<string, 'loading' | 'loaded' | 'error'>()
function preloadImage(src: string): Promise<void> {
  return new Promise((resolve) => {
    if (imageCache.has(src)) {
      resolve()
      return
    }

    imageCache.set(src, 'loading')
    const img = new Image()
    img.onload = () => {
      imageCache.set(src, 'loaded')
      resolve()
    }
    img.onerror = () => {
      imageCache.set(src, 'error')
      resolve()
    }
    img.src = src
  })
}

type ThumbnailPost = {
  id: string
  image: string[] // 修复类型定义：image 应该是字符串数组
  title: string
  user: { name: string }
  stats: { likes: string, comm: string, read: string }
  noteLink: string
  desc: string
}

interface Props {
  handleSelectNote: (noteId: string, checked: boolean) => void
  selectedNoteId?: string // 外部控制的选中ID
  useDynamicData?: boolean // 是否使用动态数据
  taskInstanceId?: string // 动态传入的 taskInstanceId
  shouldFetch?: boolean // 控制是否触发数据加载
  storeInstance?: any // 传入 store 实例
}

const RednoteReferenceList: React.FC<Props> = ({
  handleSelectNote = (_noteId: string, _checked: boolean) => {}, // 修复：提供正确的默认函数签名
  selectedNoteId: externalSelectedNoteId = '', // 接收外部传入的选中ID
  useDynamicData = false,
  taskInstanceId: propTaskInstanceId,
  shouldFetch = true,
  storeInstance,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<'All' | 'Regulars' | 'KOL' | 'KOC'>('All')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const thumbnailScrollRef = useRef<HTMLDivElement>(null)

  /** 优先使用 store 中的数据，如果没有则使用本地 state */
  const [localNoteList] = useState<{
    KOC: ThumbnailPost[]
    KOL: ThumbnailPost[]
    Regulars: ThumbnailPost[]
    All: ThumbnailPost[]
  }>({
    KOC: [],
    KOL: [],
    Regulars: [],
    All: [],
  })

  /** 从 store 获取数据或使用本地数据 */
  const [noteList, setNoteListState] = useState(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      return {
        KOC: storeInstance.stateStore.distillDarenListData.KOC || [],
        KOL: storeInstance.stateStore.distillDarenListData.KOL || [],
        Regulars: storeInstance.stateStore.distillDarenListData.Regulars || [],
        All: storeInstance.stateStore.distillDarenListData.All || [],
      }
    }
    return localNoteList
  })

  /** 🎯 骨架屏状态管理 - 与 OperationsThinkingStream1 同步 */
  const [isLoading, setIsLoading] = useState(true)
  const [operationsThinkingCompleted, setOperationsThinkingCompleted] = useState(false)

  /** 🎯 数据加载状态管理 - 与骨架屏状态分离 */
  const [localLoading, setLocalLoading] = useState(true)

  /** 🎯 使用 useSnapshot 确保响应式更新 */
  const stateSnapshot = useSnapshot(storeInstance?.stateStore || {})

  /** 🎯 综合判断 loading 状态 - 骨架屏优先，数据加载次之 */
  const loading = (isLoading || !operationsThinkingCompleted) || (storeInstance?.stateStore?.distillDarenListData?.loading ?? localLoading)

  const setLoading = useCallback((isLoading: boolean) => {
    setLocalLoading(isLoading)
    if (storeInstance?.stateStore?.distillDarenListData) {
      storeInstance.stateStore.distillDarenListData.loading = isLoading
    }
  }, [storeInstance])

  /** 监听 store 数据变化并更新本地状态 */
  const updateNoteListFromStore = useCallback(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      const storeData = storeInstance.stateStore.distillDarenListData

      /** 只有当有实际数据时才更新 */
      if (storeData.KOC?.length > 0 || storeData.KOL?.length > 0 || storeData.Regulars?.length > 0) {
        setNoteListState({
          KOC: storeData.KOC || [],
          KOL: storeData.KOL || [],
          Regulars: storeData.Regulars || [],
          All: storeData.All || [],
        })
        setLoading(false)
        setHasFetched(true)
      }
    }
  }, [storeInstance?.stateStore?.distillDarenListData, setLoading])

  useEffect(() => {
    updateNoteListFromStore()
  }, [updateNoteListFromStore])

  /** 监听store中distillDarenListData的变化 */
  useEffect(() => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      const storeData = storeInstance.stateStore.distillDarenListData

      /** 实时同步store中的数据变化 */
      if (storeData.KOC?.length > 0 || storeData.KOL?.length > 0 || storeData.Regulars?.length > 0) {
        setNoteListState({
          KOC: storeData.KOC || [],
          KOL: storeData.KOL || [],
          Regulars: storeData.Regulars || [],
          All: storeData.All || [],
        })
        setLoading(false)
      }
    }
  }, [
    storeInstance?.stateStore?.distillDarenListData?.KOC?.length,
    storeInstance?.stateStore?.distillDarenListData?.KOL?.length,
    storeInstance?.stateStore?.distillDarenListData?.Regulars?.length,
    setLoading,
  ])

  /** 🎯 监听 OperationsThinkingStream1 完成状态 - 使用 useSnapshot 确保响应式更新 */
  useEffect(() => {
    const isCompleted = stateSnapshot.operationsThinking1Completed
    console.warn('[RednoteReferenceList] useEffect 触发，检查状态:', {
      isCompleted,
      stateSnapshot: !!stateSnapshot,
      operationsThinking1Completed: stateSnapshot.operationsThinking1Completed,
    })

    if (isCompleted) {
      console.warn('[RednoteReferenceList] OperationsThinkingStream1 已完成，切换到实际内容')
      setOperationsThinkingCompleted(true)
      setIsLoading(false)
    }
    else {
      setOperationsThinkingCompleted(false)
      setIsLoading(true)
    }
  }, [stateSnapshot.operationsThinking1Completed, stateSnapshot])

  /** 如果有数据，自动设置loading为false */
  useEffect(() => {
    if (noteList.All.length > 0 && storeInstance?.stateStore?.distillDarenListData) {
      console.warn('🔍 [RednoteReferenceList] 检测到数据，设置 loading = false', {
        noteListLength: noteList.All.length,
        currentLoading: storeInstance.stateStore.distillDarenListData.loading,
        timestamp: new Date().toISOString(),
      })
      storeInstance.stateStore.distillDarenListData.loading = false
      setLocalLoading(false) // 🎯 同时更新本地状态
    }
  }, [noteList, storeInstance?.stateStore?.distillDarenListData])

  const setNoteList = useCallback((data: any) => {
    if (storeInstance?.stateStore?.distillDarenListData) {
      /** 更新 store 中的数据 */
      storeInstance.stateStore.distillDarenListData.KOC = data.KOC || []
      storeInstance.stateStore.distillDarenListData.KOL = data.KOL || []
      storeInstance.stateStore.distillDarenListData.Regulars = data.Regulars || []
      storeInstance.stateStore.distillDarenListData.All = data.All || []
    }
    /** 同时更新本地状态以触发重新渲染 */
    setNoteListState(data)
  }, [storeInstance])

  /** 使用本地状态管理 previewId，确保点击时能立即响应 */
  const [localPreviewId, setLocalPreviewId] = useState<string>(
    storeInstance?.stateStore?.distillDarenListData?.previewId || '',
  )
  const previewId = localPreviewId
  const setPreviewId = useCallback((id: string) => {
    setLocalPreviewId(id)
    if (storeInstance?.stateStore?.distillDarenListData) {
      storeInstance.stateStore.distillDarenListData.previewId = id
    }
  }, [storeInstance])

  /** 使用外部传入的选中ID，如果没有则使用store中的 */
  const selectNoteId = externalSelectedNoteId || storeInstance?.stateStore?.distillDarenListData?.selectNoteId || ''

  const [hasFetched, setHasFetched] = useState<boolean>(false) // 防止重复请求的标志
  const token = userStore.token

  const formatNumber = useCallback((num: string | number) => {
    const n = Number(num)
    if (n >= 10000)
      return `${(n / 10000).toFixed(1)}w`
    if (n >= 1000)
      return `${(n / 1000).toFixed(1)}k`
    return String(n)
  }, [])

  /** 将数据累积变量提升到组件级别，避免作用域问题 */
  const dataAccumulatorRef = useRef<{
    KOL: ThumbnailPost[]
    KOC: ThumbnailPost[]
    Regulars: ThumbnailPost[]
  }>({
    KOL: [],
    KOC: [],
    Regulars: [],
  })

  const fetchNoteList = useCallback(() => {
    /** 如果已经请求过，直接返回 */
    if (hasFetched) {
      return
    }

    /** 重置累积数据 */
    dataAccumulatorRef.current = {
      KOL: [],
      KOC: [],
      Regulars: [],
    }

    /** 如果使用动态数据，调用新的 API */
    if (useDynamicData) {
      setLoading(true)
      setHasFetched(true) // 标记已请求

      callDistillDarenListAPI(
        (data) => {
          /** 处理流式数据 */
          if (!data || !data.content)
            return

          if (data.nodeTitle === 'kol') {
            const kol = JSON.parse(data.content)
            dataAccumulatorRef.current.KOL = kol.map((item: any, index: number) => {
              const { desc, title, imageList = [], imgList = [], nick, like, comm, read } = item
              // KOL数据使用imageList字段
              return {
                id: `KOL-${index}`,
                image: imageList.length > 0
                  ? imageList
                  : imgList,
                title,
                user: { name: nick },
                stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
                noteLink: item.noteLink,
                desc,
              }
            })
          }
          if (data.nodeTitle === 'koc') {
            const koc = JSON.parse(data.content)
            dataAccumulatorRef.current.KOC = koc.map((item: any, index: number) => {
              const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
              // KOC数据使用imgList字段
              return {
                id: `KOC-${index}`,
                image: imgList.length > 0
                  ? imgList
                  : imageList,
                title,
                user: { name: nick },
                stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
                noteLink: item.noteLink,
                desc,
              }
            })
          }
          if (data.nodeTitle === 'suren') {
            const suren = JSON.parse(data.content)
            dataAccumulatorRef.current.Regulars = suren.map((item: any, index: number) => {
              const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
              // suren数据使用imgList字段
              return {
                id: `Regulars-${index}`,
                image: imgList.length > 0
                  ? imgList
                  : imageList,
                title,
                user: { name: nick },
                stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
                noteLink: item.noteLink,
                desc,
              }
            })
          }
        },
        (error) => {
          console.error('[RednoteReferenceList] 错误:', error)
          setLoading(false)
        },
        () => {
          /** 完成回调 */
          const { KOC, KOL, Regulars } = dataAccumulatorRef.current

          const allData = [...KOC, ...KOL, ...Regulars]

          setNoteList({
            KOC,
            KOL,
            Regulars,
            All: allData,
          })

          /** 根据当前选择的分类设置预览 */
          const currentCategoryData = selectedCategory === 'All'
            ? allData
            : selectedCategory === 'KOC'
              ? KOC
              : selectedCategory === 'KOL'
                ? KOL
                : Regulars

          if (currentCategoryData.length > 0) {
            const firstId = currentCategoryData[0].id
            setPreviewId(firstId)
          }

          setLoading(false)
        },
        propTaskInstanceId, // 传递 taskInstanceId
      )

      return
    }

    /** 原有的静态数据逻辑（用于测试） */
    setHasFetched(true) // 标记已请求

    /** 重置累积数据 */
    dataAccumulatorRef.current = {
      KOL: [],
      KOC: [],
      Regulars: [],
    }

    const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api'
    const taskInstanceId = propTaskInstanceId || localStorage.getItem('confirmed_taskInstanceId') || localStorage.getItem('taskInstanceId') || '1958473114904039424'

    /** 获取动态参数 */
    const dataReportData = getWorkflowOutput('data_report')
    const competitorReportData = getWorkflowOutput('competitor_report')
    const dataReportParams = JSON.parse(localStorage.getItem('dataReportParams') || '{}')

    const parameters = {
      brand_report: dataReportData.brand_report || '',
      biji_data: dataReportData.biji_data || '',
      daren_data: dataReportData.daren_data || '',
      product_data: dataReportData.product_data || '',
      industry_name: dataReportParams.industry_name || competitorReportData.industry_name || '',
    }

    fetchEventSourceWithTimeout(`${apiUrl}/app/market/stream/execute-main`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        taskInstanceId,
        platform: 'rednote',
        workflowName: 'distill_daren_list',
        parameters,
      }),
      onmessage(ev: any) {
        if (!ev.data || ev.event === 'end')
          return

        const parsed = JSON.parse(ev.data)

        if (parsed.node_title === 'kol') {
          const kol = JSON.parse(parsed.content)
          dataAccumulatorRef.current.KOL = kol.map((item: any, index: number) => {
            const { desc, title, imageList = [], imgList = [], nick, like, comm, read } = item
            // KOL数据使用imageList字段
            return {
              id: `KOL-${index}`,
              image: imageList.length > 0
                ? imageList
                : imgList,
              title,
              user: { name: nick },
              stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
              noteLink: item.noteLink,
              desc,
            }
          })
        }
        if (parsed.node_title === 'koc') {
          const koc = JSON.parse(parsed.content)
          dataAccumulatorRef.current.KOC = koc.map((item: any, index: number) => {
            const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
            // KOC数据使用imgList字段
            return {
              id: `KOC-${index}`,
              image: imgList.length > 0
                ? imgList
                : imageList,
              title,
              user: { name: nick },
              stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
              noteLink: item.noteLink,
              desc,
            }
          })
        }
        if (parsed.node_title === 'suren') {
          const suren = JSON.parse(parsed.content)
          dataAccumulatorRef.current.Regulars = suren.map((item: any, index: number) => {
            const { desc, title, imgList = [], imageList = [], nick, like, comm, read } = item
            // suren数据使用imgList字段
            return {
              id: `Regulars-${index}`,
              image: imgList.length > 0
                ? imgList
                : imageList,
              title,
              user: { name: nick },
              stats: { likes: formatNumber(like), comm: formatNumber(comm), read: formatNumber(read) },
              noteLink: item.noteLink,
              desc,
            }
          })
        }
      },
      onopen: async () => {
        setLoading(true)
      },
      onclose: async () => {
        /** 响应完成 */
        const { KOC, KOL, Regulars } = dataAccumulatorRef.current

        const allData = [...KOC, ...KOL, ...Regulars]

        setNoteList({
          KOC,
          KOL,
          Regulars,
          All: allData,
        })

        /** 根据当前选择的分类设置预览 */
        const currentCategoryData = selectedCategory === 'All'
          ? allData
          : selectedCategory === 'KOC'
            ? KOC
            : selectedCategory === 'KOL'
              ? KOL
              : Regulars

        if (currentCategoryData.length > 0) {
          const firstId = currentCategoryData[0].id
          setPreviewId(firstId)
        }

        setLoading(false)
      },
      onerror() {
        console.error('[RednoteReferenceList] 网络错误')
        setLoading(false)
      },
    })
  }, [hasFetched, useDynamicData, setLoading, setHasFetched, formatNumber, setNoteList, selectedCategory, setPreviewId, propTaskInstanceId, token])

  const handlePreviewPost = useCallback((postId: string) => {
    setPreviewId(postId)
    setCurrentImageIndex(0) // 重置图片索引

    /** 预加载当前选中项的图片 */
    const selectedPost = noteList.All.find((item: ThumbnailPost) => item.id === postId)
    if (selectedPost?.image?.length > 0) {
      selectedPost.image.slice(0, 3).forEach((src: string) => {
        if (src && typeof src === 'string') {
          preloadImage(src).catch(() => {
            /** 静默处理预加载失败 */
          })
        }
      })
    }
  }, [setPreviewId, noteList.All])

  /** 智能滑动函数 - 根据屏幕尺寸调整滑动距离 */
  const handleThumbnailScroll = useCallback((direction: 'left' | 'right') => {
    const container = thumbnailScrollRef.current
    if (!container)
      return

    const containerWidth = container.clientWidth

    /** 根据屏幕尺寸计算每次显示的卡片数量 */
    const getCardsPerView = () => {
      if (containerWidth < 640)
        return 2 // 移动端显示2个
      if (containerWidth < 1024)
        return 3 // 平板显示3个
      return 5 // 桌面显示5个
    }

    const cardsPerView = getCardsPerView()
    const scrollAmount = containerWidth * (cardsPerView / (cardsPerView + 0.5)) // 精确滑动到下一组

    container.scrollBy({
      left: direction === 'right'
        ? scrollAmount
        : -scrollAmount,
      behavior: 'smooth',
    })
  }, [])

  useEffect(() => {
    /** 优先使用 store 中的数据 - 如果 ChatPage 已经加载过数据，直接使用 */
    if (storeInstance?.stateStore?.distillDarenListData) {
      const storeData = storeInstance.stateStore.distillDarenListData

      /** 如果 store 中有数据，直接使用 */
      if (storeData.KOC?.length > 0 || storeData.KOL?.length > 0 || storeData.Regulars?.length > 0) {
        setNoteListState({
          KOC: storeData.KOC || [],
          KOL: storeData.KOL || [],
          Regulars: storeData.Regulars || [],
          All: storeData.All || [],
        })

        /** 设置默认预览 */
        const allData = storeData.All || []
        if (allData.length > 0 && !localPreviewId) {
          const firstId = allData[0].id
          setPreviewId(firstId)
        }

        setLoading(false)
        setHasFetched(true)
        return
      }
    }

    /** 如果 store 中没有数据但已经触发过工作流，等待数据 */
    if (storeInstance?.stateStore?.hasTriggeredDistillDarenList) {
      /** 不要重复请求，等待 ChatPage 中的工作流完成 */
      setLoading(true)
      return
    }

    /** 只有在没有 store 且没有触发过工作流的情况下才自己调用 API */
    if (shouldFetch && !hasFetched && useDynamicData) {
      fetchNoteList()
    }
    else if (!shouldFetch || hasFetched) {
      setLoading(false)
    }
  }, [useDynamicData, propTaskInstanceId, shouldFetch, hasFetched, localPreviewId, storeInstance?.stateStore?.distillDarenListData, storeInstance?.stateStore?.hasTriggeredDistillDarenList, setLoading, setPreviewId, fetchNoteList])

  const preview = useMemo(() => {
    /** 首先在当前分类中查找 */
    let found = noteList[selectedCategory].find((item: ThumbnailPost) => item.id === previewId)
    /** 如果当前分类没找到，在All中查找 */
    if (!found) {
      found = noteList.All.find((item: ThumbnailPost) => item.id === previewId)
    }

    /** 如果还是没找到，使用第一个可用的项目 */
    if (!found && noteList.All.length > 0) {
      found = noteList.All[0]
      /** 同时更新 previewId */
      if (found) {
        setPreviewId(found.id)
      }
    }

    /** 如果仍然没有数据，创建一个测试数据 */
    if (!found && process.env.NODE_ENV === 'development') {
      found = {
        id: 'test-1',
        image: [
          'https://picsum.photos/800/600?random=1',
          'https://picsum.photos/800/600?random=2',
          'https://picsum.photos/800/600?random=3',
        ],
        title: '测试标题 - 北方·盛夏·大海',
        user: { name: '测试用户' },
        stats: { likes: '1.5k', comm: '706', read: '172517' },
        noteLink: '#',
        desc: '这是一个测试描述，用于验证轮播组件是否正常工作。',
      }
    }

    return found
  }, [previewId, selectedCategory, noteList, setPreviewId])

  return (
    <div className="bg-white p-6">
      {/* Header */}
      <div className="mb-6">
        <h2 className="mb-2 text-2xl text-gray-900 font-semibold">Rednote Reference List</h2>
        <p className="text-sm text-gray-600 leading-relaxed">
          Choose your template Select a reference from your Rednote list to use as your starting point
        </p>
      </div>

      {loading
        ? (
            <div className="fade-in">
              <RednoteReferenceSkeleton />
            </div>
          )
        : noteList.All.length === 0
          ? (
              <div className="fade-in py-8 text-center text-gray-500">
                <p>Waiting for distill_daren_list workflow to complete...</p>
                <p className="mt-2 text-xs">数据加载中，请稍候</p>
              </div>
            )
          : (
              <div className="fade-in">
                {/* 真实内容的淡入动画 */}
                <div style={ { overflow: 'auto' } }>
                  {/* Category Filter */}
                  <div className="flex items-center py-4">
                    <span className="mr-4 text-sm text-gray-700 font-medium">Post Categories:</span>
                    <Select
                      value={ selectedCategory }
                      onChange={ (e) => {
                        setSelectedCategory(e)
                        setCurrentImageIndex(0) // 重置图片索引
                        /** 默认显示第一个 */
                        const firstItem = noteList[e]?.[0]
                        if (firstItem) {
                          setPreviewId(firstItem.id)
                        }
                      } }
                      className="!h-8 !min-w-[90px] !flex !items-center !rounded-full !border-none !bg-[#f5f5f5] !px-3 !py-1 !shadow-none"
                      dropdownStyle={ { minWidth: 120, whiteSpace: 'normal' } }
                      optionLabelProp="children"
                      suffixIcon={
                        <span className="ml-1 text-xs text-gray-400">
                          <ChevronDown size={ 13 } />
                        </span>
                      }
                    >
                      <Select.Option value="All" className="!text-gray-700 !font-medium">All</Select.Option>
                      <Select.Option value="KOC" className="!text-gray-700 !font-medium">KOC</Select.Option>
                      <Select.Option value="KOL" className="!text-gray-700 !font-medium">KOL</Select.Option>
                      <Select.Option value="Regulars" className="!text-gray-700 !font-medium">Regulars</Select.Option>
                    </Select>
                  </div>

                  {/* Main Content Card */}
                  <div
                    className={ classNames(`mb-2 h-134 flex overflow-hidden border-2 border-blue-100 rounded-xl`) }
                    style={ {
                      background: preview?.id === selectNoteId
                        ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
                        : 'transparent',
                    } }>
                    {/* Image Section - 使用新的轮播组件 */}
                    <div className="relative flex-1">
                      <ImageCarousel
                        images={ preview?.image || [] }
                        currentIndex={ currentImageIndex }
                        onIndexChange={ setCurrentImageIndex }
                        showCounter={ true }
                        showIndicators={ true }
                        maxIndicators={ 5 }
                      />
                    </div>

                    {/* Content Section */}
                    <div className="flex flex-1 flex-col p-6">
                      {/* User Info */}
                      <div className="mb-4 flex items-center justify-between">
                        <div className="flex items-center">
                          <img
                            src="/src/assets/image/home/<USER>/1.webp"
                            alt="User avatar"
                            className="mr-2 h-8 w-8 rounded-full"
                          />
                          <span className="mr-2 text-gray-700 font-medium">{preview?.user.name}</span>
                        </div>
                        <span className="text-[rgb(104 104 104)] rounded-xl bg-[#f5f5f5] px-2 py-1 text-xs">
                          {preview?.id.split('-')[0]}
                        </span>
                      </div>

                      {/* Post Content */}
                      <div className="mb-4 flex-1">
                        <h3 className="mb-3 text-base text-gray-700 font-semibold leading-tight">
                          {preview?.title}
                        </h3>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          {preview?.desc}
                        </p>
                      </div>

                      {/* Stats and Actions */}
                      <div className="mb-4 flex items-center justify-between border-t border-gray-200 pt-4">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1 text-sm text-gray-600">
                            <Heart size={ 16 } />
                            {preview?.stats.likes}
                          </span>
                          <span className="flex items-center gap-1 text-sm text-gray-600">
                            <MessageCircle size={ 16 } />
                            {preview?.stats.comm}
                          </span>
                          <span className="flex items-center gap-1 text-sm text-gray-600">
                            <Eye size={ 16 } />
                            {preview?.stats.read}
                          </span>
                        </div>
                        <Button
                          type="link"
                          className="flex items-center gap-1 text-sm !p-0 !text-blue-600"
                          onClick={ () => window.open(preview?.noteLink) }
                        >
                          View Post
                          <Link size={ 13 } />
                        </Button>
                      </div>

                      {/* Select Reference Checkbox */}
                      <div className="flex items-center justify-end gap-2">
                        <Checkbox
                          onChange={ (e) => {
                            const checked = e.target.checked
                            if (preview?.id) {
                              handleSelectNote(preview.id, checked) // 通知父组件
                            }
                          } }
                          checked={ preview?.id === selectNoteId }>
                          Select as my reference
                        </Checkbox>
                      </div>
                    </div>
                  </div>

                  {/* Thumbnail Carousel */}
                  <div className="relative flex items-center gap-3">
                    <button
                      className="h-8 w-8 flex flex-shrink-0 items-center justify-center border border-gray-300 rounded-full bg-black bg-opacity-60 text-base text-white transition-all duration-200 active:scale-95 hover:scale-110 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                      onClick={ () => handleThumbnailScroll('left') }
                      aria-label="Previous thumbnails"
                    >
                      ‹
                    </button>
                    <div
                      ref={ thumbnailScrollRef }
                      className="thumbnail-scroll-container scrollbar-hide flex flex-1 gap-4 overflow-x-auto scroll-smooth py-2"
                    >
                      {noteList[selectedCategory]
                        .map((post: ThumbnailPost) => (
                          <ThumbnailCard
                            key={ post.id }
                            post={ post }
                            isSelected={ post.id === selectNoteId }
                            isPreview={ preview?.id === post.id }
                            onPreview={ handlePreviewPost }
                            selectNoteId={ selectNoteId }
                          />
                        ))}
                    </div>
                    <button
                      className="h-8 w-8 flex flex-shrink-0 items-center justify-center border border-gray-300 rounded-full bg-black bg-opacity-60 text-base text-white transition-all duration-200 active:scale-95 hover:scale-110 hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                      onClick={ () => handleThumbnailScroll('right') }
                      aria-label="Next thumbnails"
                    >
                      ›
                    </button>
                  </div>
                </div>
              </div>
            )}

    </div>
  )
}

export default RednoteReferenceList
