import {
  CSSProperties,
  FC,
  ImgHTMLAttributes,
  useCallback,
  useState,
} from "react"
import classNames from 'clsx'
import { Image } from 'lucide-react'

import styles from "./index.module.less"

interface Props extends ImgHTMLAttributes<HTMLImageElement> {
  className?: string
  imgClassName?: string
  style?: CSSProperties
}

const SquareImage: FC<Props> = ({
  src,
  className,
  imgClassName,
  style,
  ...props
}) => {
  const [loadError, setLoadError] = useState(false)

  const onImageLoadError = useCallback(() => {
    setLoadError(true)
  }, [])

  if (src && !loadError) {
    return (
      <div className={classNames(styles["square"], className)}>
        <img
          src={src}
          alt="image"
          className={classNames(styles["image"], imgClassName)}
          style={style}
          onError={onImageLoadError}
          draggable="false"
          {...props}
        />
      </div>
    )
  } else {
    return (
      <div className={classNames(styles["square"], className)}>
        <Image className={styles["error-icon"]} />
      </div>
    )
  }
}

export default SquareImage
