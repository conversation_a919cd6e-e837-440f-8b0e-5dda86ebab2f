import type { DropdownProps } from '@/components/Dropdown'
import { Dropdown } from '@/components/Dropdown'
import { isMobileDevice } from '@/config'
import { clsx } from 'clsx'
import { memo } from 'react'

export const Faq = memo<FaqProps>((
  {
    style,
    className,
    items,
  },
) => {
  return <div
    className={ clsx(
      'FaqContainer h-fit',
      isMobileDevice
        ? 'py-8'
        : 'py-16',
      className,
    ) }
    style={ style }
  >
    <h3 className={ clsx(
      'text-center text-white font-medium',
      isMobileDevice
        ? 'mb-8 mt-8 text-2xl'
        : 'mb-16 mt-16 text-4xl md:text-5xl',
    ) }>
      FAQ
    </h3>

    <div className={ clsx(
      'mx-auto max-w-7xl',
      isMobileDevice
        ? 'px-4 py-4'
        : 'py-8',
    ) }>
      <Dropdown
        items={ items }
        className={ ` text-white space-y-4 ` }
        sectionHeaderClassName={ clsx(
          'font-bold !text-white',
          isMobileDevice
            ? 'text-base'
            : 'text-lg',
        ) }
        itemTitleClassName="!text-gray-400"
        itemDescClassName="!text-gray-400"
        itemInactiveClassName="hover:bg-[transparent]/10"
        itemClassName={ clsx(
          `border border-zinc-600/50 rounded-xl transition-colors duration-300 space-y-3 hover:border-zinc-500/50`,
          isMobileDevice
            ? 'px-3 py-2 text-sm'
            : 'py-3',
        ) }
      ></Dropdown>
    </div>
  </div>
})

Faq.displayName = 'Faq'

export type FaqProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  items: DropdownProps['items']
}
