import { clsx } from 'clsx'
import { memo } from 'react'

export const BlurBgImg = memo<BlurBgImgProps>((
  {
    style,
    className,
    imgClassName,
    img,
    children,
    blur = '15px',
    ...imgProps
  },
) => {
  return <div
    className={ clsx(
      'BlurBgImgContainer relative overflow-hidden rounded-xl',
      className,
    ) }
    style={ style }
  >
    {(img && /.mp4/.test(img))
      ? <video
          src={ img }
          className="absolute left-0 top-0 object-cover"
          autoPlay
          muted
          loop
          playsInline
          webkit-playsinline="true"
          x-webkit-airplay="deny"
          x5-video-player-type="h5"
          x5-video-orientation="portraint"
          style={ {
            width: '125%',
            height: '125%',
            filter: `blur(${blur})`,
          } }
        />
      : <img
          src={ img }
          alt="Background Image"
          className="absolute left-0 top-0 object-cover"
          style={ {
            width: '125%',
            height: '125%',
            filter: `blur(${blur})`,
          } }
        />}

    <div className={ clsx(
      'relative z-2 size-full flex items-center justify-center',
      imgClassName,
    ) }>
      {
        children
          ? <>{ children }</>
          : (img && /.mp4/.test(img)
              ? <video
                  autoPlay
                  muted
                  loop
                  playsInline
                  webkit-playsinline="true"
                  x-webkit-airplay="deny"
                  x5-video-player-type="h5"
                  x5-video-orientation="portraint"
                  src={ img }
                  className="h-full rounded-2xl object-contain"
                  { ...imgProps }
                />
              : <img
                  src={ img }
                  className="h-full object-contain"
                  { ...imgProps }
                />)
      }
    </div>
  </div>
})

BlurBgImg.displayName = 'BlurBgImg'

export type BlurBgImgProps = {
  className?: string
  imgClassName?: string
  style?: React.CSSProperties
  children?: React.ReactNode
  img: string
  blur?: string
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement> & React.DetailedHTMLProps<React.VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>
