import Logo from '@/components/Logo'
import SvgIcon from '@/components/SvgIcon'
import { IS_PROD } from '@/config'
import { RechargeTypeEnum } from '@/dts'
import { onMounted, useNavi, useT } from '@/hooks'
import { updateUserInfo, userDetailStore } from '@/store/userStore'
import { Popover } from 'antd'
import cn from 'clsx'
import { motion } from 'framer-motion'
import React, { memo, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { useSnapshot } from 'valtio'
import { UserInfo } from './UserInfo'

type NavItem = {
  name: string
  pathname: string
}

type NavbarProps = {
  className?: string
  style?: React.CSSProperties
}

const Navbar = memo(({ className, style }: NavbarProps) => {
  const to = useNavi()
  const t = useT()
  const userinfo = useSnapshot(userDetailStore)
  const hasAvatar = userinfo.avatar && userinfo.avatar !== ''
  const { pathname } = useLocation()

  /** 添加选中状态，初始值根据当前路径设置 */
  const [selectedTab, setSelectedTab] = useState(() => {
    if (pathname.startsWith('/p/photog'))
      return '/p/photog'
    if (pathname.startsWith('/p/trend'))
      return '/p/trend'
    if (pathname.startsWith('/p/video'))
      return '/p/video'
    return '/p/trend' // 默认选中
  })

  onMounted(() => {
    updateUserInfo()
  })

  const navItems: NavItem[] = IS_PROD
    ? [
        { name: 'Home', pathname: '/p/trend' },
        { name: 'Picture', pathname: '/p/photog' },
        { name: 'Video', pathname: '/p/video' },
      ]
    : [
        { name: t('layout.nav-home'), pathname: '/p/trend' },
        // { name: t('layout.nav-square'), pathname: '/p/history' },
        { name: t('layout.nav-photo'), pathname: '/p/photog' },
        { name: t('layout.nav-video'), pathname: '/p/video' },
        // { name: t('layout.nav-items'), pathname: '/p/models' },
        // { name: t('layout.nav-assets'), pathname: '/p/assets' },
        // { name: t('layout.nav-lab'), pathname: '/p/lab' },
      ]

  return (
    <motion.header
      initial={ { y: -20, opacity: 0 } }
      animate={ { y: 0, opacity: 1 } }
      transition={ { duration: 0.5, ease: 'easeOut' } }
      className={ cn(
        'w-full  flex items-center justify-between border-b border-gray-100 backdrop-blur-sm relative',
        className,
      ) }

      style={ { ...style, height: '90px' } }
    >
      <Logo className="flex items-center" logoColor="#333"></Logo>

      {/* 导航标签 - 白色背景的圆角容器 */}
      <nav className="mx-6 rounded-3xl bg-white px-6 py-2 shadow-sm">
        <div className="flex items-center">
          { navItems.map(item => (
            <NavLink
              key={ item.name }
              item={ item }
              isSelected={ selectedTab === item.pathname }
              onSelect={ () => setSelectedTab(item.pathname) }
            />
          )) }
        </div>
      </nav>

      {/* 用户信息区域 */}
      <div className="flex items-center gap-4">
        {/* 皇冠图标 */}
        <div className="flex cursor-pointer items-center gap-2 overflow-hidden rounded-full bg-white p-2 transition hover:bg-innerBg">
          <SvgIcon icon="vip" className="h-6 w-6 text-yellow-500" />
        </div>

        {/* 积分显示 */}
        <div className="flex items-center gap-2">
          <div className="flex cursor-pointer items-center gap-2 overflow-hidden rounded-full bg-white p-2 transition hover:bg-innerBg">
            <SvgIcon icon="credit3" noFill className="h-6 w-6 text-blue-600" />
          </div>
          <span className="text-sm text-gray-700 font-medium">{ userinfo.totalCredits || '1234' }</span>
        </div>

        {/* 用户头像 */}
        <Popover
          content={ <UserInfo></UserInfo> }
        >
          <div
            className={ cn(
              'relative size-8 cursor-pointer overflow-hidden border border-gray-300 rounded-full border-solid bg-white',
              hasAvatar
                ? ''
                : 'p-1.2',
            ) }
          >
            <img
              src={ userinfo.avatar || new URL('@/assets/svg/avatar.svg', import.meta.url).href }
              alt=""
              className={ cn(
                {
                  'size-full object-cover': hasAvatar,
                },
              ) }
            />
          </div>
        </Popover>
      </div>
    </motion.header>
  )
})

Navbar.displayName = 'Navbar'

const NavLink = memo(({
  item,
  isSelected,
  onSelect,
}: {
  item: NavItem
  isSelected: boolean
  onSelect: () => void
}) => {
  const to = useNavi()

  const handleClick = () => {
    onSelect() // 更新选中状态
    to(item.pathname as any) // 导航到对应页面
  }

  return (
    <motion.a
      className={ cn(
        'relative px-1 py-2 text-sm font-medium transition-colors',

      ) }
      onClick={ handleClick }
      style={ {
        border: isSelected
          ? '2px solid transparent'
          : '2px solid transparent',
        background: isSelected
          ? 'linear-gradient(white, white) padding-box, linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%) border-box'
          : 'linear-gradient(white, white) padding-box, transparent border-box',
        borderRadius: '16px',
        padding: '3px 24px',
      } }
      data-id={ item.pathname }
    >

      <span className="font-bold">
        { item.name }
      </span>
    </motion.a>
  )
})

NavLink.displayName = 'NavLink'

export default Navbar
